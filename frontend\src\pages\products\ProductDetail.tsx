import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import axios from 'axios';
import { FaEdit, FaArrowLeft, FaBox, FaHistory, FaShoppingCart } from 'react-icons/fa';
import '../../styles/SharedStyles.css';

interface Material {
  id: number;
  material_name: string;
  quantity: number;
  rate: number;
  total_cost: number;
  vendor_id?: number;
  vendor_name?: string;
}

interface Batch {
  id: number;
  batch_number: string;
  quantity_produced: number;
  production_date: string;
  total_cost: number;
  cost_per_unit: number;
  materials: Material[];
}

interface Sale {
  id: number;
  sale_id: number;
  quantity: number;
  unit_price: number;
  total_price: number;
  profit: number;
  sale_date: string;
}

interface Product {
  id: number;
  name: string;
  description: string;
  stock: number;
  created_at: string;
  batches: Batch[];
  sales: Sale[];
}

const ProductDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [product, setProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        const response = await axios.get(`http://localhost:5000/api/products/${id}`);
        setProduct(response.data);
        setLoading(false);
      } catch (err) {
        setError('Failed to fetch product details. Please try again later.');
        setLoading(false);
        console.error('Error fetching product:', err);
      }
    };

    fetchProduct();
  }, [id]);

  if (loading) {
    return <div className="app-loading">Loading product details...</div>;
  }

  if (error) {
    return <div className="app-error">{error}</div>;
  }

  if (!product) {
    return <div className="app-error">Product not found</div>;
  }

  return (
    <div className="app-form-container">
      <div className="app-header">
        <h1>{product.name}</h1>
        <div className="app-header-actions">
          <Link to="/products" className="app-btn app-btn-sm">
            <FaArrowLeft /> Back to Products
          </Link>
          <Link to={`/products/edit/${product.id}`} className="app-btn app-btn-sm app-btn-primary">
            <FaEdit /> Edit Product
          </Link>
        </div>
      </div>

      <div className="app-card">
        <h2 className="app-section-header">Product Information</h2>

        <div className="app-form-row">
          <div className="app-form-col">
            <div className="app-form-group">
              <label className="app-form-label">Description</label>
              <div className="app-form-value">
                {product.description || 'No description provided'}
              </div>
            </div>
          </div>

          <div className="app-form-col">
            <div className="app-form-group">
              <label className="app-form-label">Current Stock</label>
              <div className="app-form-value" style={{
                color: product.stock > 10 ? '#2ecc71' : product.stock > 0 ? '#f39c12' : '#e74c3c',
                fontWeight: 600
              }}>
                {product.stock} units
              </div>
            </div>
          </div>

          <div className="app-form-col">
            <div className="app-form-group">
              <label className="app-form-label">Created At</label>
              <div className="app-form-value">
                {new Date(product.created_at).toLocaleDateString()}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="app-card">
        <h2 className="app-section-header">
          <FaBox style={{ marginRight: '0.5rem' }} /> Production Batches
        </h2>

        {product.batches && product.batches.length > 0 ? (
          <div>
            <table className="app-table">
              <thead>
                <tr>
                  <th>Batch Number</th>
                  <th>Production Date</th>
                  <th>Quantity</th>
                  <th>Total Cost</th>
                  <th>Cost Per Unit</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {product.batches.map(batch => (
                  <tr key={batch.id}>
                    <td><strong>{batch.batch_number}</strong></td>
                    <td>{new Date(batch.production_date).toLocaleDateString()}</td>
                    <td>{batch.quantity_produced}</td>
                    <td><span style={{ color: '#3498db', fontWeight: 600 }}>PKR {parseFloat(batch.total_cost || 0).toFixed(2)}</span></td>
                    <td><span style={{ color: '#2ecc71', fontWeight: 600 }}>PKR {parseFloat(batch.cost_per_unit || 0).toFixed(2)}</span></td>
                    <td>
                      <Link to={`/batches/${batch.id}`} className="app-btn app-btn-sm app-btn-primary">
                        View Details
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            <h3 style={{ margin: '2rem 0 1rem', color: '#2c3e50', borderBottom: '1px solid #eaeaea', paddingBottom: '0.5rem' }}>
              Raw Materials & Vendors
            </h3>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
              {product.batches.map(batch => (
                <div key={`materials-${batch.id}`} className="app-card" style={{ margin: 0, backgroundColor: '#f8f9fa' }}>
                  <h4 style={{ marginBottom: '1rem', color: '#2c3e50', display: 'flex', alignItems: 'center' }}>
                    <span style={{
                      display: 'inline-block',
                      backgroundColor: '#3498db',
                      color: 'white',
                      width: '24px',
                      height: '24px',
                      borderRadius: '50%',
                      textAlign: 'center',
                      lineHeight: '24px',
                      marginRight: '0.5rem',
                      fontSize: '0.8rem'
                    }}>
                      B
                    </span>
                    Batch #{batch.batch_number} Materials
                  </h4>

                  {batch.materials && batch.materials.length > 0 ? (
                    <table className="app-table">
                      <thead>
                        <tr>
                          <th>Material</th>
                          <th>Vendor</th>
                          <th>Quantity</th>
                          <th>Rate</th>
                          <th>Total Cost</th>
                        </tr>
                      </thead>
                      <tbody>
                        {batch.materials.map(material => (
                          <tr key={material.id}>
                            <td><strong>{material.material_name}</strong></td>
                            <td>
                              {material.vendor_name ? (
                                <Link to={`/vendors/${material.vendor_id}`} style={{ color: '#3498db', textDecoration: 'none' }}>
                                  {material.vendor_name}
                                </Link>
                              ) : (
                                'Not specified'
                              )}
                            </td>
                            <td>{material.quantity}</td>
                            <td>PKR {parseFloat(material.rate || 0).toFixed(2)}</td>
                            <td><span style={{ color: '#3498db', fontWeight: 600 }}>PKR {parseFloat(material.total_cost || 0).toFixed(2)}</span></td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  ) : (
                    <p style={{ textAlign: 'center', padding: '1rem', color: '#666' }}>No materials found for this batch.</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        ) : (
          <p style={{ textAlign: 'center', padding: '2rem', color: '#666' }}>No production batches found for this product.</p>
        )}
      </div>

      <div className="app-card">
        <h2 className="app-section-header">
          <FaShoppingCart style={{ marginRight: '0.5rem' }} /> Sales History
        </h2>

        {product.sales && product.sales.length > 0 ? (
          <table className="app-table">
            <thead>
              <tr>
                <th>Date</th>
                <th>Quantity</th>
                <th>Unit Price</th>
                <th>Total</th>
                <th>Profit</th>
              </tr>
            </thead>
            <tbody>
              {product.sales.map(sale => (
                <tr key={sale.id}>
                  <td>{new Date(sale.sale_date).toLocaleDateString()}</td>
                  <td>{sale.quantity}</td>
                  <td>PKR {parseFloat(sale.unit_price || 0).toFixed(2)}</td>
                  <td><span style={{ color: '#3498db', fontWeight: 600 }}>PKR {parseFloat(sale.total_price || 0).toFixed(2)}</span></td>
                  <td><span style={{ color: '#2ecc71', fontWeight: 600 }}>PKR {parseFloat(sale.profit || 0).toFixed(2)}</span></td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <p style={{ textAlign: 'center', padding: '2rem', color: '#666' }}>No sales history found for this product.</p>
        )}
      </div>
    </div>
  );
};

export default ProductDetail;
