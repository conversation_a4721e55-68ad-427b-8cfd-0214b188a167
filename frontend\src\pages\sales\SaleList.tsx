import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { FaPlus, FaTrash, FaEye, FaMoneyBillWave } from 'react-icons/fa';
import '../../styles/SharedStyles.css';

interface Sale {
  id: number;
  invoice_number: string;
  customer_name: string;
  sale_date: string;
  total_amount: number;
  paid_amount: number; // Changed from payment_received to paid_amount to match backend
  balance: number;
  profit: number;
}

const SaleList = () => {
  const [sales, setSales] = useState<Sale[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSales = async () => {
      try {
        const response = await axios.get('http://localhost:5000/api/sales');
        setSales(response.data);
        setLoading(false);
      } catch (err) {
        setError('Failed to fetch sales. Please try again later.');
        setLoading(false);
        console.error('Error fetching sales:', err);
      }
    };

    fetchSales();
  }, []);

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this sale?')) {
      try {
        await axios.delete(`http://localhost:5000/api/sales/${id}`);
        setSales(sales.filter(sale => sale.id !== id));
      } catch (err) {
        setError('Failed to delete sale. Please try again later.');
        console.error('Error deleting sale:', err);
      }
    }
  };

  if (loading) {
    return <div className="app-loading">Loading sales...</div>;
  }

  if (error) {
    return <div className="app-error">{error}</div>;
  }

  return (
    <div className="app-form-container">
      <div className="app-header">
        <h1>
          <FaMoneyBillWave style={{ marginRight: '0.5rem', color: '#2ecc71' }} /> Sales
        </h1>
        <div className="app-header-actions">
          <Link to="/sales/new" className="app-btn app-btn-primary">
            <FaPlus /> Add Sale
          </Link>
        </div>
      </div>

      {sales.length === 0 ? (
        <div className="app-card" style={{ textAlign: 'center', padding: '3rem' }}>
          <p style={{ fontSize: '1.1rem', color: '#666' }}>No sales found. Start by adding a sale.</p>
          <Link to="/sales/new" className="app-btn app-btn-primary" style={{ marginTop: '1rem' }}>
            <FaPlus /> Add Your First Sale
          </Link>
        </div>
      ) : (
        <div className="app-card">
          <table className="app-table">
            <thead>
              <tr>
                <th>Invoice #</th>
                <th>Customer</th>
                <th>Date</th>
                <th>Total Amount</th>
                <th>Payment Received</th>
                <th>Balance</th>
                <th>Profit</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {sales.map(sale => (
                <tr key={sale.id}>
                  <td><strong>{sale.invoice_number}</strong></td>
                  <td>{sale.customer_name}</td>
                  <td>{new Date(sale.sale_date).toLocaleDateString()}</td>
                  <td><span style={{ color: '#3498db', fontWeight: 600 }}>PKR {parseFloat(sale.total_amount || 0).toFixed(2)}</span></td>
                  <td><span style={{ color: '#2ecc71', fontWeight: 600 }}>PKR {parseFloat(sale.paid_amount || 0).toFixed(2)}</span></td>
                  <td>
                    <span style={{
                      color: parseFloat(sale.balance || 0) > 0 ? '#e74c3c' : '#2ecc71',
                      fontWeight: 600
                    }}>
                      PKR {parseFloat(sale.balance || 0).toFixed(2)}
                    </span>
                  </td>
                  <td><span style={{ color: '#2ecc71', fontWeight: 600 }}>PKR {parseFloat(sale.profit || 0).toFixed(2)}</span></td>
                  <td className="app-table-actions">
                    <Link to={`/sales/${sale.id}`} className="app-btn app-btn-icon" title="View Details">
                      <FaEye />
                    </Link>
                    <button
                      onClick={() => handleDelete(sale.id)}
                      className="app-btn app-btn-icon app-btn-danger"
                      title="Delete"
                    >
                      <FaTrash />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default SaleList;
