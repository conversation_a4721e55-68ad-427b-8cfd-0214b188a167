import { Link } from 'react-router-dom';
import { FaChartLine, FaBox, FaUserTie, FaUsers, FaShoppingCart } from 'react-icons/fa';

const Navbar = () => {
  return (
    <nav className="navbar">
      <div className="navbar-brand">
        <Link to="/">
          <FaChartLine className="brand-icon" />
          <span>Stock Management System</span>
        </Link>
      </div>
      <ul className="navbar-nav">
        <li className="nav-item">
          <Link to="/" className="nav-link">
            Dashboard
          </Link>
        </li>
        <li className="nav-item">
          <Link to="/products" className="nav-link">
            Products
          </Link>
        </li>
        <li className="nav-item">
          <Link to="/vendors" className="nav-link">
            Vendors
          </Link>
        </li>
        <li className="nav-item">
          <Link to="/customers" className="nav-link">
            Customers
          </Link>
        </li>
        <li className="nav-item">
          <Link to="/sales" className="nav-link">
            Sales
          </Link>
        </li>
      </ul>
    </nav>
  );
};

export default Navbar;
