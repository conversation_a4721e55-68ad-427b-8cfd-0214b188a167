# Product Costing, Vendor & Customer Khata, and Sales Management System

## Project Overview

This system is designed to help manufacturing businesses manage their entire operation from production to sales, with a focus on cost tracking and financial management. The system integrates product management, batch production, inventory control, vendor/customer relationships, and sales in a unified platform.

## Technology Stack

- **Frontend**: React
- **Backend**: Node.js with Express
- **Database**: PostgreSQL
- **Currency**: PKR (Pakistani Rupee)

## Core Modules

### 1. Product Management
- Create and manage product catalog
- Store product details including name and description
- View comprehensive product listings

### 2. Batch-wise Production & Expense Management
- Track production batches with unique batch numbers
- Record quantity produced in each batch
- Add and track raw materials used in production
- Calculate total raw material expenses
- Determine cost per unit based on batch expenses and quantity

### 3. Stock/Inventory Management
- Automatic stock updates after production
- Automatic stock reduction after sales
- Real-time stock level monitoring
- Stock alerts (potential enhancement)

### 4. Vendor Khata (Ledger)
- Vendor profile management
- Track raw material purchases from vendors
- Record payments to vendors
- Calculate outstanding balances
- Generate vendor-specific financial reports

### 5. Customer <PERSON> (Ledger)
- Customer profile management
- Track sales to customers
- Record payments from customers
- Calculate outstanding balances
- Generate customer-specific financial reports

### 6. Sales System
- Record sales transactions
- Link sales to specific customers
- Calculate total sale amount
- Track payment status
- Calculate profit margins (sale price vs. cost price)

### 7. Reporting
- Batch cost analysis
- Current inventory status
- Vendor payment status
- Customer payment status
- Sales performance (daily/monthly)
- Profit analysis

## Additional Features
- User-friendly interface for non-technical users
- Data editing and deletion capabilities
- Report export functionality (PDF/Excel)
- Data validation and error handling
