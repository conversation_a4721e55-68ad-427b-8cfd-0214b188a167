import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import axios from 'axios';
import { FaArrowLeft, FaEdit } from 'react-icons/fa';
import './BatchStyles.css';

interface Material {
  id: number;
  material_name: string; // Changed from name to material_name to match backend
  quantity: number;
  rate: number; // Changed from unit_cost to rate to match backend
  total_cost: number;
  vendor_id?: number;
  vendor_name?: string;
}

interface Batch {
  id: number;
  batch_number: string;
  product_id: number;
  product_name: string;
  quantity_produced: number;
  production_date: string;
  total_cost: number;
  cost_per_unit: number;
  materials: Material[];
}

const BatchDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [batch, setBatch] = useState<Batch | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBatch = async () => {
      try {
        const response = await axios.get(`http://localhost:5000/api/batches/${id}`);
        setBatch(response.data);
        setLoading(false);
      } catch (err) {
        setError('Failed to fetch batch details. Please try again later.');
        setLoading(false);
        console.error('Error fetching batch:', err);
      }
    };

    fetchBatch();
  }, [id]);

  if (loading) {
    return <div className="batch-loading">Loading batch details...</div>;
  }

  if (error) {
    return <div className="batch-error">{error}</div>;
  }

  if (!batch) {
    return <div className="batch-error">Batch not found</div>;
  }

  return (
    <div className="batch-form-container">
      <div className="batch-header">
        <h1>Batch #{batch.batch_number}</h1>
        <div className="batch-header-actions">
          <Link to="/batches" className="batch-btn batch-btn-sm">
            <FaArrowLeft /> Back to Batches
          </Link>
          <Link to={`/batches/edit/${batch.id}`} className="batch-btn batch-btn-sm batch-btn-primary">
            <FaEdit /> Edit Batch
          </Link>
        </div>
      </div>

      <div className="batch-card">
        <h2 style={{ marginBottom: '1.5rem', color: '#2c3e50', borderBottom: '1px solid #eaeaea', paddingBottom: '0.75rem' }}>Batch Information</h2>

        <div className="batch-form-row" style={{ marginBottom: '1.5rem' }}>
          <div className="batch-form-col">
            <div className="batch-form-group">
              <label className="batch-form-label">Product</label>
              <div className="batch-form-value">{batch.product_name}</div>
            </div>
          </div>

          <div className="batch-form-col">
            <div className="batch-form-group">
              <label className="batch-form-label">Quantity Produced</label>
              <div className="batch-form-value">{batch.quantity_produced} units</div>
            </div>
          </div>
        </div>

        <div className="batch-form-row" style={{ marginBottom: '1.5rem' }}>
          <div className="batch-form-col">
            <div className="batch-form-group">
              <label className="batch-form-label">Production Date</label>
              <div className="batch-form-value">{new Date(batch.production_date).toLocaleDateString()}</div>
            </div>
          </div>

          <div className="batch-form-col">
            <div className="batch-form-group">
              <label className="batch-form-label">Total Cost</label>
              <div className="batch-form-value" style={{ color: '#3498db', fontWeight: 600 }}>
                PKR {parseFloat(batch.total_cost).toFixed(2)}
              </div>
            </div>
          </div>

          <div className="batch-form-col">
            <div className="batch-form-group">
              <label className="batch-form-label">Cost Per Unit</label>
              <div className="batch-form-value" style={{ color: '#2ecc71', fontWeight: 600 }}>
                PKR {parseFloat(batch.cost_per_unit).toFixed(2)}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="batch-card">
        <h2 style={{ marginBottom: '1.5rem', color: '#2c3e50', borderBottom: '1px solid #eaeaea', paddingBottom: '0.75rem' }}>Materials Used</h2>

        {batch.materials && batch.materials.length > 0 ? (
          <table className="batch-table">
            <thead>
              <tr>
                <th>Material</th>
                <th>Vendor</th>
                <th>Quantity</th>
                <th>Unit Cost</th>
                <th>Total Cost</th>
              </tr>
            </thead>
            <tbody>
              {batch.materials.map(material => (
                <tr key={material.id}>
                  <td><strong>{material.material_name}</strong></td>
                  <td>
                    {material.vendor_name ? (
                      <Link to={`/vendors/${material.vendor_id}`} style={{ color: '#3498db', textDecoration: 'none' }}>
                        {material.vendor_name}
                      </Link>
                    ) : (
                      'Not specified'
                    )}
                  </td>
                  <td>{material.quantity}</td>
                  <td>PKR {parseFloat(material.rate).toFixed(2)}</td>
                  <td><span style={{ color: '#3498db', fontWeight: 600 }}>PKR {parseFloat(material.total_cost).toFixed(2)}</span></td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <p style={{ textAlign: 'center', padding: '2rem', color: '#666' }}>No materials found for this batch.</p>
        )}
      </div>
    </div>
  );
};

export default BatchDetail;
