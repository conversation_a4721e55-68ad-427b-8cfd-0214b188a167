const db = require('../db/db');

// @desc    Get all batches
// @route   GET /api/batches
// @access  Public
const getBatches = async (req, res) => {
  try {
    const result = await db.query(
      'SELECT b.*, p.name as product_name FROM batches b JOIN products p ON b.product_id = p.id ORDER BY b.production_date DESC'
    );
    res.status(200).json(result.rows);
  } catch (error) {
    console.error('Error fetching batches:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get single batch
// @route   GET /api/batches/:id
// @access  Public
const getBatchById = async (req, res) => {
  try {
    const { id } = req.params;

    // Get batch details
    const batchResult = await db.query(
      'SELECT b.*, p.name as product_name FROM batches b JOIN products p ON b.product_id = p.id WHERE b.id = $1',
      [id]
    );

    if (batchResult.rows.length === 0) {
      return res.status(404).json({ message: 'Batch not found' });
    }

    const batch = batchResult.rows[0];

    // Get materials used in this batch with vendor information
    const materialsResult = await db.query(
      `SELECT bm.*, v.name as vendor_name
       FROM batch_materials bm
       LEFT JOIN vendors v ON bm.vendor_id = v.id
       WHERE bm.batch_id = $1
       ORDER BY bm.id`,
      [id]
    );

    res.status(200).json({
      ...batch,
      materials: materialsResult.rows
    });
  } catch (error) {
    console.error('Error fetching batch:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Create a batch
// @route   POST /api/batches
// @access  Public
const createBatch = async (req, res) => {
  const client = await db.pool.connect();

  try {
    const {
      product_id,
      batch_number,
      quantity_produced,
      production_date,
      materials
    } = req.body;

    if (!product_id || !batch_number || !quantity_produced || !materials || !materials.length) {
      return res.status(400).json({
        message: 'Product ID, batch number, quantity produced, and at least one material are required'
      });
    }

    await client.query('BEGIN');

    // Calculate total cost from materials
    let total_cost = 0;
    materials.forEach(material => {
      // Ensure total_cost is calculated if not provided
      const materialTotalCost = material.total_cost || (material.quantity * material.rate);
      material.total_cost = materialTotalCost; // Update the material object
      total_cost += parseFloat(materialTotalCost);
    });

    // Calculate cost per unit
    const cost_per_unit = total_cost / quantity_produced;

    // Insert batch
    const batchResult = await client.query(
      'INSERT INTO batches (product_id, batch_number, quantity_produced, production_date, total_cost, cost_per_unit) VALUES ($1, $2, $3, $4, $5, $6) RETURNING *',
      [product_id, batch_number, quantity_produced, production_date || new Date(), total_cost, cost_per_unit]
    );

    const batch_id = batchResult.rows[0].id;

    // Insert materials and create vendor transactions if vendor_id is provided
    for (const material of materials) {
      // Use the total_cost that was already calculated and updated above
      await client.query(
        'INSERT INTO batch_materials (batch_id, material_name, quantity, rate, total_cost, vendor_id) VALUES ($1, $2, $3, $4, $5, $6)',
        [batch_id, material.material_name, material.quantity, material.rate, material.total_cost, material.vendor_id || null]
      );

      // If vendor_id is provided, create a vendor transaction
      if (material.vendor_id) {
        // Determine payment status (default to unpaid)
        const payment_status = 'unpaid';

        // Create vendor transaction
        await client.query(
          `INSERT INTO vendor_transactions
            (vendor_id, transaction_date, material_name, quantity, rate, total_amount, paid_amount, payment_status, notes)
           VALUES
            ($1, $2, $3, $4, $5, $6, $7, $8, $9)`,
          [
            material.vendor_id,
            production_date || new Date(),
            material.material_name,
            material.quantity,
            material.rate,
            material.total_cost,
            0, // paid_amount (default to 0)
            payment_status,
            `Used in Batch #${batch_number}`
          ]
        );
      }
    }

    // Update inventory
    const inventoryResult = await client.query(
      'SELECT * FROM inventory WHERE product_id = $1',
      [product_id]
    );

    if (inventoryResult.rows.length > 0) {
      await client.query(
        'UPDATE inventory SET quantity = quantity + $1, updated_at = CURRENT_TIMESTAMP WHERE product_id = $2',
        [quantity_produced, product_id]
      );
    } else {
      await client.query(
        'INSERT INTO inventory (product_id, quantity) VALUES ($1, $2)',
        [product_id, quantity_produced]
      );
    }

    await client.query('COMMIT');

    // Get the complete batch with materials
    const completeBatchResult = await client.query(
      'SELECT b.*, p.name as product_name FROM batches b JOIN products p ON b.product_id = p.id WHERE b.id = $1',
      [batch_id]
    );

    // Get materials with vendor information
    const materialsResult = await client.query(
      `SELECT bm.*, v.name as vendor_name
       FROM batch_materials bm
       LEFT JOIN vendors v ON bm.vendor_id = v.id
       WHERE bm.batch_id = $1
       ORDER BY bm.id`,
      [batch_id]
    );

    res.status(201).json({
      ...completeBatchResult.rows[0],
      materials: materialsResult.rows
    });
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error creating batch:', error);

    // Provide more detailed error message
    const errorMessage = error.detail || error.message || 'Server error';
    res.status(500).json({
      message: 'Error creating batch',
      error: errorMessage
    });
  } finally {
    client.release();
  }
};

// @desc    Get batches by product ID
// @route   GET /api/products/:id/batches
// @access  Public
const getBatchesByProductId = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query(
      'SELECT * FROM batches WHERE product_id = $1 ORDER BY production_date DESC',
      [id]
    );

    res.status(200).json(result.rows);
  } catch (error) {
    console.error('Error fetching product batches:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Delete a batch
// @route   DELETE /api/batches/:id
// @access  Public
const deleteBatch = async (req, res) => {
  const client = await db.pool.connect();

  try {
    const { id } = req.params;

    await client.query('BEGIN');

    // Get batch details before deletion
    const batchResult = await client.query(
      'SELECT * FROM batches WHERE id = $1',
      [id]
    );

    if (batchResult.rows.length === 0) {
      await client.query('ROLLBACK');
      return res.status(404).json({ message: 'Batch not found' });
    }

    const batch = batchResult.rows[0];

    // Update inventory (reduce quantity)
    await client.query(
      'UPDATE inventory SET quantity = quantity - $1, updated_at = CURRENT_TIMESTAMP WHERE product_id = $2',
      [batch.quantity_produced, batch.product_id]
    );

    // Delete batch (will cascade to batch_materials)
    await client.query(
      'DELETE FROM batches WHERE id = $1',
      [id]
    );

    await client.query('COMMIT');

    res.status(200).json({ message: 'Batch deleted successfully' });
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error deleting batch:', error);
    res.status(500).json({ message: 'Server error' });
  } finally {
    client.release();
  }
};

// @desc    Update a batch
// @route   PUT /api/batches/:id
// @access  Public
const updateBatch = async (req, res) => {
  const client = await db.pool.connect();

  try {
    const { id } = req.params;
    const {
      product_id,
      batch_number,
      quantity_produced,
      production_date,
      materials
    } = req.body;

    if (!product_id || !batch_number || !quantity_produced || !materials || !materials.length) {
      return res.status(400).json({
        message: 'Product ID, batch number, quantity produced, and at least one material are required'
      });
    }

    await client.query('BEGIN');

    // Get the original batch to calculate inventory adjustment
    const originalBatchResult = await client.query(
      'SELECT * FROM batches WHERE id = $1',
      [id]
    );

    if (originalBatchResult.rows.length === 0) {
      await client.query('ROLLBACK');
      return res.status(404).json({ message: 'Batch not found' });
    }

    const originalBatch = originalBatchResult.rows[0];
    const originalQuantity = originalBatch.quantity_produced;

    // Calculate total cost from materials
    let total_cost = 0;
    materials.forEach(material => {
      // For direct total cost input, we use the provided total_cost
      total_cost += parseFloat(material.total_cost || 0);
    });

    // Calculate cost per unit
    const cost_per_unit = total_cost / quantity_produced;

    // Update batch
    await client.query(
      'UPDATE batches SET product_id = $1, batch_number = $2, quantity_produced = $3, production_date = $4, total_cost = $5, cost_per_unit = $6, updated_at = CURRENT_TIMESTAMP WHERE id = $7',
      [product_id, batch_number, quantity_produced, production_date || new Date(), total_cost, cost_per_unit, id]
    );

    // Delete existing materials for this batch
    await client.query(
      'DELETE FROM batch_materials WHERE batch_id = $1',
      [id]
    );

    // Insert updated materials
    for (const material of materials) {
      // Set default values for quantity and rate if they're not provided
      const quantity = material.quantity || 1;
      const rate = material.rate || 0;

      await client.query(
        'INSERT INTO batch_materials (batch_id, material_name, quantity, rate, total_cost, vendor_id) VALUES ($1, $2, $3, $4, $5, $6)',
        [id, material.material_name, quantity, rate, material.total_cost, material.vendor_id || null]
      );
    }

    // Update inventory
    // First, adjust for the original quantity
    await client.query(
      'UPDATE inventory SET quantity = quantity - $1, updated_at = CURRENT_TIMESTAMP WHERE product_id = $2',
      [originalQuantity, originalBatch.product_id]
    );

    // Then, add the new quantity
    const inventoryResult = await client.query(
      'SELECT * FROM inventory WHERE product_id = $1',
      [product_id]
    );

    if (inventoryResult.rows.length > 0) {
      await client.query(
        'UPDATE inventory SET quantity = quantity + $1, updated_at = CURRENT_TIMESTAMP WHERE product_id = $2',
        [quantity_produced, product_id]
      );
    } else {
      await client.query(
        'INSERT INTO inventory (product_id, quantity) VALUES ($1, $2)',
        [product_id, quantity_produced]
      );
    }

    await client.query('COMMIT');

    // Get the complete updated batch with materials
    const completeBatchResult = await client.query(
      'SELECT b.*, p.name as product_name FROM batches b JOIN products p ON b.product_id = p.id WHERE b.id = $1',
      [id]
    );

    // Get materials with vendor information
    const materialsResult = await client.query(
      `SELECT bm.*, v.name as vendor_name
       FROM batch_materials bm
       LEFT JOIN vendors v ON bm.vendor_id = v.id
       WHERE bm.batch_id = $1
       ORDER BY bm.id`,
      [id]
    );

    res.status(200).json({
      ...completeBatchResult.rows[0],
      materials: materialsResult.rows
    });
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error updating batch:', error);

    // Provide more detailed error message
    const errorMessage = error.detail || error.message || 'Server error';
    res.status(500).json({
      message: 'Error updating batch',
      error: errorMessage
    });
  } finally {
    client.release();
  }
};

module.exports = {
  getBatches,
  getBatchById,
  createBatch,
  updateBatch,
  getBatchesByProductId,
  deleteBatch
};
