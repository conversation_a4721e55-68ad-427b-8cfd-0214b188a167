import { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { FaArrowLeft, FaSave, FaPlus, FaTrash, FaShoppingCart, FaMoneyBillWave } from 'react-icons/fa';
import '../../styles/SharedStyles.css';

interface Product {
  id: number;
  name: string;
  stock: number;
  cost_per_unit: number;
}

interface Customer {
  id: number;
  name: string;
}

interface SaleItem {
  id: string;
  product_id: number;
  quantity: number;
  unit_price: number;
  product?: Product;
}

interface SaleFormData {
  customer_id: number;
  sale_date: string;
  payment_received: number;
  items: SaleItem[];
}

const SaleForm = () => {
  const navigate = useNavigate();
  const [products, setProducts] = useState<Product[]>([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [formData, setFormData] = useState<SaleFormData>({
    customer_id: 0,
    sale_date: new Date().toISOString().split('T')[0],
    payment_received: 0,
    items: [{ id: Date.now().toString(), product_id: 0, quantity: 1, unit_price: 0 }]
  });
  const [loading, setLoading] = useState<boolean>(true);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [productsResponse, customersResponse] = await Promise.all([
          axios.get('http://localhost:5000/api/products'),
          axios.get('http://localhost:5000/api/customers')
        ]);

        // Ensure each product has a cost_per_unit value
        const productsWithCost = productsResponse.data.map(product => ({
          ...product,
          cost_per_unit: product.cost_per_unit || 1 // Default to 1 if not available
        }));

        setProducts(productsWithCost);
        setCustomers(customersResponse.data);
        setLoading(false);
      } catch (err) {
        setError('Failed to fetch data. Please try again later.');
        setLoading(false);
        console.error('Error fetching data:', err);
      }
    };

    fetchData();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: ['customer_id'].includes(name) ? parseInt(value) :
              name === 'payment_received' ? parseFloat(value) : value
    }));
  };

  const handleItemChange = (id: string, field: keyof SaleItem, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.map(item => {
        if (item.id === id) {
          const updatedItem = { ...item, [field]: value };

          // If product_id changed, update the unit_price with the default price
          if (field === 'product_id') {
            const selectedProduct = products.find(p => p.id === Number(value));
            if (selectedProduct) {
              updatedItem.product = selectedProduct;
              // You might want to set a default price based on the product
              // updatedItem.unit_price = selectedProduct.default_price || 0;
            }
          }

          return updatedItem;
        }
        return item;
      })
    }));
  };

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { id: Date.now().toString(), product_id: 0, quantity: 1, unit_price: 0 }]
    }));
  };

  const removeItem = (id: string) => {
    if (formData.items.length > 1) {
      setFormData(prev => ({
        ...prev,
        items: prev.items.filter(item => item.id !== id)
      }));
    }
  };

  const calculateTotalAmount = () => {
    return formData.items.reduce((total, item) => {
      return total + (item.quantity * item.unit_price);
    }, 0);
  };

  const calculateBalance = () => {
    const totalAmount = calculateTotalAmount();
    const paymentReceived = typeof formData.payment_received === 'number' ?
                           formData.payment_received :
                           parseFloat(formData.payment_received.toString()) || 0;
    return totalAmount - paymentReceived;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Add total_price and cost_price to each item
      const itemsWithPrices = formData.items.map(item => {
        const selectedProduct = products.find(p => p.id === item.product_id);
        const cost_price = selectedProduct?.cost_per_unit || 1; // Default to 1 to avoid constraint violation

        return {
          ...item,
          total_price: item.quantity * item.unit_price,
          cost_price: cost_price
        };
      });

      const response = await axios.post('http://localhost:5000/api/sales', {
        ...formData,
        items: itemsWithPrices,
        total_amount: calculateTotalAmount(),
        paid_amount: formData.payment_received, // Use paid_amount instead of payment_received to match backend
        balance: calculateBalance()
      });
      setSuccess('Sale created successfully!');
      setTimeout(() => navigate(`/sales/${response.data.id}`), 1500);
    } catch (err) {
      setError('Failed to save sale. Please try again later.');
      console.error('Error saving sale:', err);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return <div className="app-loading">Loading data...</div>;
  }

  return (
    <div className="app-form-container">
      <div className="app-header">
        <h1>
          <FaShoppingCart style={{ marginRight: '0.5rem', color: '#3498db' }} /> Create New Sale
        </h1>
        <div className="app-header-actions">
          <Link to="/sales" className="app-btn app-btn-sm">
            <FaArrowLeft /> Back to Sales
          </Link>
        </div>
      </div>

      <div className="app-card">
        {error && <div className="app-error">{error}</div>}
        {success && <div className="app-success">{success}</div>}

        <form onSubmit={handleSubmit}>
          <div className="app-form-row">
            <div className="app-form-col">
              <div className="app-form-group">
                <label htmlFor="customer_id" className="app-form-label">Customer</label>
                <select
                  id="customer_id"
                  name="customer_id"
                  className="app-form-control"
                  value={formData.customer_id}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select a customer</option>
                  {customers.map(customer => (
                    <option key={customer.id} value={customer.id}>{customer.name}</option>
                  ))}
                </select>
              </div>
            </div>

            <div className="app-form-col">
              <div className="app-form-group">
                <label htmlFor="sale_date" className="app-form-label">Sale Date</label>
                <input
                  type="date"
                  id="sale_date"
                  name="sale_date"
                  className="app-form-control"
                  value={formData.sale_date}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>
          </div>

          <h3 className="app-section-header">
            <FaShoppingCart style={{ marginRight: '0.5rem' }} /> Sale Items
          </h3>

          {formData.items.map((item, index) => (
            <div key={item.id} className="material-card">
              <div className="material-header">
                <h4 className="material-title">Item #{index + 1}</h4>
                <button
                  type="button"
                  className="app-btn app-btn-sm app-btn-danger"
                  onClick={() => removeItem(item.id)}
                  disabled={formData.items.length <= 1}
                >
                  <FaTrash /> Remove
                </button>
              </div>

              <div className="app-form-row">
                <div className="app-form-col">
                  <div className="app-form-group">
                    <label className="app-form-label">Product</label>
                    <select
                      className="app-form-control"
                      value={item.product_id}
                      onChange={(e) => handleItemChange(item.id, 'product_id', parseInt(e.target.value))}
                      required
                    >
                      <option value="">Select a product</option>
                      {products.map(product => (
                        <option key={product.id} value={product.id}>
                          {product.name} (Stock: {product.stock})
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>

              <div className="app-form-row">
                <div className="app-form-col">
                  <div className="app-form-group">
                    <label className="app-form-label">Quantity</label>
                    <input
                      type="number"
                      className="app-form-control"
                      value={item.quantity}
                      onChange={(e) => handleItemChange(item.id, 'quantity', parseInt(e.target.value))}
                      min="1"
                      required
                    />
                  </div>
                </div>

                <div className="app-form-col">
                  <div className="app-form-group">
                    <label className="app-form-label">Unit Price (PKR)</label>
                    <input
                      type="number"
                      className="app-form-control"
                      value={item.unit_price}
                      onChange={(e) => handleItemChange(item.id, 'unit_price', parseFloat(e.target.value))}
                      min="0.01"
                      step="0.01"
                      required
                    />
                  </div>
                </div>

                <div className="app-form-col">
                  <div className="app-form-group">
                    <label className="app-form-label">Total</label>
                    <div className="app-form-value" style={{ color: '#3498db', fontWeight: 600 }}>
                      PKR {(item.quantity * item.unit_price).toFixed(2)}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}

          <button
            type="button"
            className="app-btn app-btn-secondary"
            onClick={addItem}
            style={{ marginBottom: '1.5rem' }}
          >
            <FaPlus /> Add Item
          </button>

          <div className="summary-card">
            <h3>
              <FaMoneyBillWave style={{ marginRight: '0.5rem' }} /> Payment Information
            </h3>

            <div className="summary-item">
              <span className="summary-label">Total Amount:</span>
              <span className="summary-value">PKR {calculateTotalAmount().toFixed(2)}</span>
            </div>

            <div className="app-form-group" style={{ margin: '1.5rem 0' }}>
              <label htmlFor="payment_received" className="app-form-label">Payment Received</label>
              <input
                type="number"
                id="payment_received"
                name="payment_received"
                className="app-form-control"
                value={formData.payment_received}
                onChange={handleChange}
                min="0"
                step="0.01"
                required
              />
            </div>

            <div className="summary-item">
              <span className="summary-label">Balance:</span>
              <span className="summary-value" style={{
                color: calculateBalance() > 0 ? '#e74c3c' : '#2ecc71'
              }}>
                PKR {calculateBalance().toFixed(2)}
              </span>
            </div>
          </div>

          <div style={{ textAlign: 'center', marginTop: '2rem' }}>
            <button
              type="submit"
              className="app-btn app-btn-primary"
              disabled={submitting}
              style={{ minWidth: '200px' }}
            >
              <FaSave /> {submitting ? 'Saving...' : 'Create Sale'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SaleForm;
