const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());

// Root route
app.get('/', (req, res) => {
  res.json({ message: 'Welcome to the Product Costing and Khata Management API' });
});

// Test route
app.post('/api/test', (req, res) => {
  console.log('Test endpoint called');
  console.log('Request body:', req.body);
  res.json({ message: 'Test successful', data: req.body });
});

// Routes
app.use('/api/products', require('./routes/productRoutes'));
app.use('/api/simple-products', require('./routes/simpleProductRoutes'));
app.use('/api/batches', require('./routes/batchRoutes'));
app.use('/api/vendors', require('./routes/vendorRoutes'));
app.use('/api/customers', require('./routes/customerRoutes'));
app.use('/api/sales', require('./routes/saleRoutes'));
app.use('/api/dashboard', require('./routes/dashboardRoutes'));

// Start the server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
