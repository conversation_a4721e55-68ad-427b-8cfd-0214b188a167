import { useState, useEffect } from 'react';
import { FaBox, FaUserTie, FaUsers, FaShoppingCart, FaMoneyBillWave, FaChartLine, FaHandHoldingUsd, FaArrowUp, FaArrowDown, FaCalendarAlt, FaExchangeAlt } from 'react-icons/fa';
import { Link } from 'react-router-dom';
import axios from 'axios';

const Dashboard = () => {
  const [stats, setStats] = useState({
    products: 0,
    vendors: 0,
    customers: 0,
    sales: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [recentActivity, setRecentActivity] = useState({
    sales: [],
    vendorTransactions: []
  });

  const [summary, setSummary] = useState({
    customer: {
      total_sales: 0,
      total_received: 0,
      total_owed: 0
    },
    vendor: {
      total_purchases: 0,
      total_paid: 0,
      total_owed: 0
    },
    profit: 0
  });

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Fetch dashboard stats from the new endpoint
        const response = await axios.get('http://localhost:5000/api/dashboard/stats');

        setStats(response.data.stats);
        setSummary(response.data.summary);
        setRecentActivity(response.data.recentActivity);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError('Failed to load dashboard data');
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PK', {
      style: 'currency',
      currency: 'PKR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Get current date
  const currentDate = new Date().toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  return (
    <div className="dashboard-container">
      <div className="dashboard-header">
        <h1>Dashboard</h1>
        <div className="dashboard-date">
          <FaCalendarAlt /> {currentDate}
        </div>
      </div>

      {error && <div className="alert alert-danger">{error}</div>}

      {loading ? (
        <div className="dashboard-loading">
          <div className="spinner"></div>
          <p>Loading dashboard data...</p>
        </div>
      ) : (
        <>
          {/* Stats Overview */}
          <div className="dashboard-stats-grid">
            <div className="dashboard-stat-card products">
              <div className="stat-icon">
                <FaBox />
              </div>
              <div className="stat-content">
                <h3>{stats.products}</h3>
                <p>Products</p>
              </div>
              <Link to="/products" className="stat-link">View</Link>
            </div>

            <div className="dashboard-stat-card vendors">
              <div className="stat-icon">
                <FaUserTie />
              </div>
              <div className="stat-content">
                <h3>{stats.vendors}</h3>
                <p>Vendors</p>
              </div>
              <Link to="/vendors" className="stat-link">View</Link>
            </div>

            <div className="dashboard-stat-card customers">
              <div className="stat-icon">
                <FaUsers />
              </div>
              <div className="stat-content">
                <h3>{stats.customers}</h3>
                <p>Customers</p>
              </div>
              <Link to="/customers" className="stat-link">View</Link>
            </div>

            <div className="dashboard-stat-card sales">
              <div className="stat-icon">
                <FaShoppingCart />
              </div>
              <div className="stat-content">
                <h3>{stats.sales}</h3>
                <p>Sales</p>
              </div>
              <Link to="/sales" className="stat-link">View</Link>
            </div>
          </div>

          <div className="dashboard-main-grid">
            {/* Financial Summary */}
            <div className="dashboard-card financial-summary">
              <div className="card-header">
                <h2>Financial Summary</h2>
              </div>
              <div className="financial-metrics">
                <div className="metric-card profit">
                  <div className="metric-icon">
                    <FaChartLine />
                  </div>
                  <div className="metric-content">
                    <h3>{formatCurrency(parseFloat(summary.profit))}</h3>
                    <p>Total Profit</p>
                  </div>
                </div>

                <div className="metric-card receivables">
                  <div className="metric-icon">
                    <FaArrowUp />
                  </div>
                  <div className="metric-content">
                    <h3>{formatCurrency(parseFloat(summary.customer.total_owed))}</h3>
                    <p>Customer Receivables</p>
                  </div>
                </div>

                <div className="metric-card payables">
                  <div className="metric-icon">
                    <FaArrowDown />
                  </div>
                  <div className="metric-content">
                    <h3>{formatCurrency(parseFloat(summary.vendor.total_owed))}</h3>
                    <p>Vendor Payables</p>
                  </div>
                </div>

                <div className="metric-card cashflow">
                  <div className="metric-icon">
                    <FaMoneyBillWave />
                  </div>
                  <div className="metric-content">
                    <h3>{formatCurrency(parseFloat(summary.customer.total_received) - parseFloat(summary.vendor.total_paid))}</h3>
                    <p>Net Cash Flow</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Recent Transactions */}
            <div className="dashboard-card recent-transactions">
              <div className="card-header">
                <h2>Recent Transactions</h2>
                <div className="card-actions">
                  <FaExchangeAlt />
                </div>
              </div>
              <div className="transactions-content">
                {recentActivity.sales && recentActivity.sales.length > 0 ? (
                  <div className="transaction-list">
                    {recentActivity.sales.slice(0, 3).map((sale: any) => (
                      <div key={sale.id} className="transaction-item">
                        <div className="transaction-icon sale">
                          <FaShoppingCart />
                        </div>
                        <div className="transaction-details">
                          <div className="transaction-title">
                            {sale.invoice_number || `Sale #${sale.id}`}
                          </div>
                          <div className="transaction-subtitle">
                            {sale.customer_name || 'Walk-in Customer'} • {new Date(sale.sale_date).toLocaleDateString()}
                          </div>
                        </div>
                        <div className="transaction-amount">
                          {formatCurrency(parseFloat(sale.total_amount))}
                        </div>
                      </div>
                    ))}
                    <Link to="/sales" className="view-all-link">View all sales</Link>
                  </div>
                ) : (
                  <p className="no-data">No recent sales found.</p>
                )}

                {recentActivity.vendorTransactions && recentActivity.vendorTransactions.length > 0 ? (
                  <div className="transaction-list">
                    {recentActivity.vendorTransactions.slice(0, 3).map((transaction: any) => (
                      <div key={transaction.id} className="transaction-item">
                        <div className="transaction-icon purchase">
                          <FaUserTie />
                        </div>
                        <div className="transaction-details">
                          <div className="transaction-title">
                            {transaction.vendor_name}
                          </div>
                          <div className="transaction-subtitle">
                            {transaction.material_name} • {new Date(transaction.transaction_date).toLocaleDateString()}
                          </div>
                        </div>
                        <div className="transaction-amount">
                          {formatCurrency(parseFloat(transaction.total_amount))}
                        </div>
                      </div>
                    ))}
                    <Link to="/vendors" className="view-all-link">View all purchases</Link>
                  </div>
                ) : (
                  <p className="no-data">No recent purchases found.</p>
                )}
              </div>
            </div>
          </div>

          {/* Balance Summary */}
          <div className="dashboard-card balance-summary">
            <div className="card-header">
              <h2>Balance Summary</h2>
            </div>
            <div className="balance-metrics">
              <div className="balance-metric">
                <div className="balance-label">Total Sales</div>
                <div className="balance-value">{formatCurrency(parseFloat(summary.customer.total_sales))}</div>
              </div>
              <div className="balance-metric">
                <div className="balance-label">Received</div>
                <div className="balance-value">{formatCurrency(parseFloat(summary.customer.total_received))}</div>
              </div>
              <div className="balance-metric">
                <div className="balance-label">Total Purchases</div>
                <div className="balance-value">{formatCurrency(parseFloat(summary.vendor.total_purchases))}</div>
              </div>
              <div className="balance-metric">
                <div className="balance-label">Paid</div>
                <div className="balance-value">{formatCurrency(parseFloat(summary.vendor.total_paid))}</div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default Dashboard;
