const db = require('../db/db');

// @desc    Get dashboard statistics
// @route   GET /api/dashboard/stats
// @access  Public
const getDashboardStats = async (req, res) => {
  try {
    // Get product count
    const productsResult = await db.query('SELECT COUNT(*) as count FROM products');

    // Get vendor count
    const vendorsResult = await db.query('SELECT COUNT(*) as count FROM vendors');

    // Get customer count
    const customersResult = await db.query('SELECT COUNT(*) as count FROM customers');

    // Get sales count
    const salesResult = await db.query('SELECT COUNT(*) as count FROM sales');

    // Get customer summary
    const customerSummaryResult = await db.query(`
      SELECT
        COALESCE(SUM(CASE WHEN transaction_type = 'sale' THEN amount ELSE 0 END), 0) as total_sales,
        COALESCE(SUM(CASE WHEN transaction_type = 'payment' THEN amount ELSE 0 END), 0) as total_received,
        COALESCE(
          SUM(CASE WHEN transaction_type = 'sale' THEN amount ELSE 0 END) -
          SUM(CASE WHEN transaction_type = 'payment' THEN amount ELSE 0 END),
          0
        ) as total_owed
      FROM
        customer_transactions
    `);

    // Get vendor summary
    const vendorSummaryResult = await db.query(`
      SELECT
        COALESCE(SUM(CASE WHEN material_name != 'Payment' THEN total_amount ELSE 0 END), 0) as total_purchases,
        COALESCE(SUM(CASE WHEN material_name = 'Payment' THEN paid_amount ELSE 0 END), 0) as total_paid,
        COALESCE(
          SUM(CASE WHEN material_name != 'Payment' THEN total_amount ELSE 0 END) -
          SUM(CASE WHEN material_name = 'Payment' THEN paid_amount ELSE 0 END),
          0
        ) as total_owed
      FROM
        vendor_transactions
    `);

    // Get total profit
    const profitResult = await db.query(`
      SELECT COALESCE(SUM(profit), 0) as total_profit FROM sale_items
    `);

    // Get recent sales
    const recentSalesResult = await db.query(`
      SELECT
        s.id,
        s.invoice_number,
        s.sale_date,
        s.total_amount,
        s.payment_status,
        c.name as customer_name
      FROM
        sales s
      LEFT JOIN
        customers c ON s.customer_id = c.id
      ORDER BY
        s.sale_date DESC
      LIMIT 5
    `);

    // Get recent vendor transactions
    const recentVendorTransactionsResult = await db.query(`
      SELECT
        vt.id,
        vt.transaction_date,
        vt.material_name,
        vt.total_amount,
        vt.payment_status,
        v.name as vendor_name
      FROM
        vendor_transactions vt
      JOIN
        vendors v ON vt.vendor_id = v.id
      ORDER BY
        vt.transaction_date DESC
      LIMIT 5
    `);

    res.status(200).json({
      stats: {
        products: parseInt(productsResult.rows[0].count),
        vendors: parseInt(vendorsResult.rows[0].count),
        customers: parseInt(customersResult.rows[0].count),
        sales: parseInt(salesResult.rows[0].count)
      },
      summary: {
        customer: customerSummaryResult.rows[0],
        vendor: vendorSummaryResult.rows[0],
        profit: parseFloat(profitResult.rows[0].total_profit)
      },
      recentActivity: {
        sales: recentSalesResult.rows,
        vendorTransactions: recentVendorTransactionsResult.rows
      }
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

module.exports = {
  getDashboardStats
};
