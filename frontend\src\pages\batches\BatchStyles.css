/* BatchStyles.css - Custom styling for batch-related components */

/* Batch Card Styles */
.batch-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 1.75rem;
  margin-bottom: 1.75rem;
  transition: all 0.3s ease;
}

.batch-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* Batch Header Styles */
.batch-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eaeaea;
}

.batch-header h1 {
  font-size: 1.75rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.batch-header-actions {
  display: flex;
  gap: 12px;
}

/* Batch Form Styles */
.batch-form-container {
  max-width: 1200px;
  margin: 0 auto;
}

.batch-form-group {
  margin-bottom: 1.5rem;
}

.batch-form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.batch-form-col {
  flex: 1;
  min-width: 250px;
}

.batch-form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.95rem;
}

.batch-form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.batch-form-control:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
  outline: none;
}

.batch-form-control::placeholder {
  color: #aaa;
}

.batch-form-value {
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  font-size: 1rem;
  min-height: 42px;
  display: flex;
  align-items: center;
}

/* Material Card Styles */
.material-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.25rem;
  border: 1px solid #eaeaea;
  transition: all 0.2s ease;
}

.material-card:hover {
  border-color: #ddd;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.material-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.material-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

/* Button Styles */
.batch-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.6rem 1.2rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: 8px;
}

.batch-btn-primary {
  background-color: #3498db;
  color: white;
}

.batch-btn-primary:hover {
  background-color: #2980b9;
}

.batch-btn-secondary {
  background-color: #2ecc71;
  color: white;
}

.batch-btn-secondary:hover {
  background-color: #27ae60;
}

.batch-btn-danger {
  background-color: #e74c3c;
  color: white;
}

.batch-btn-danger:hover {
  background-color: #c0392b;
}

.batch-btn-sm {
  padding: 0.4rem 0.8rem;
  font-size: 0.875rem;
}

.batch-btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  padding: 0;
}

/* Cost Summary Styles */
.cost-summary {
  background-color: #f1f8fe;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1.5rem 0;
  border-left: 4px solid #3498db;
}

.cost-summary h3 {
  margin-top: 0;
  color: #2c3e50;
  font-size: 1.2rem;
  margin-bottom: 1rem;
}

.cost-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.cost-label {
  font-weight: 600;
  color: #2c3e50;
}

.cost-value {
  font-weight: 700;
  color: #3498db;
}

/* Table Styles */
.batch-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 1.5rem;
}

.batch-table th {
  background-color: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
  text-align: left;
  padding: 1rem;
  border-bottom: 2px solid #eaeaea;
}

.batch-table td {
  padding: 1rem;
  border-bottom: 1px solid #eaeaea;
  vertical-align: middle;
}

.batch-table tr:last-child td {
  border-bottom: none;
}

.batch-table tr:hover td {
  background-color: #f8f9fa;
}

.batch-table-actions {
  display: flex;
  gap: 8px;
}

/* Loading and Error States */
.batch-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.batch-error {
  background-color: #fdf3f2;
  color: #e74c3c;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  border-left: 4px solid #e74c3c;
}

.batch-success {
  background-color: #eafaf1;
  color: #27ae60;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  border-left: 4px solid #27ae60;
}
