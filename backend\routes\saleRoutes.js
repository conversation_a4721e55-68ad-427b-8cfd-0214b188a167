const express = require('express');
const router = express.Router();
const {
  getSales,
  getSaleById,
  createSale,
  deleteSale,
  addPayment
} = require('../controllers/saleController');

// Routes for /api/sales
router.route('/')
  .get(getSales)
  .post(createSale);

router.route('/:id')
  .get(getSaleById)
  .delete(deleteSale);

// Route for adding payment to a sale
router.route('/:id/payment')
  .post(addPayment);

module.exports = router;
