# Product Costing, Vendor & Customer Khata, and Sales Management System

A comprehensive business management system for manufacturing operations.

## Project Structure

- `/frontend` - React frontend application
- `/backend` - Node.js/Express backend application
- `/docs` - Project documentation

## Technology Stack

- **Frontend**: React with Vite
- **Backend**: Node.js with Express
- **Database**: PostgreSQL
- **Currency**: PKR (Pakistani Rupee)

## Getting Started

### Prerequisites

- Node.js (v14+)
- npm or yarn
- PostgreSQL (v12+)

### Installation

1. Clone the repository
2. Set up the backend:
   ```
   cd backend
   npm install
   ```
3. Set up the frontend:
   ```
   cd frontend
   npm install
   ```
4. Configure the database connection in `backend/.env`
5. Start the development servers:
   - Backend: `npm run dev` in the backend directory
   - Frontend: `npm run dev` in the frontend directory

## Features

See [project_overview.md](./project_overview.md) for a complete list of features.

## Development Plan

See [implementation_plan.md](./implementation_plan.md) for the detailed development plan.
