{"name": "backend", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js", "init-db": "node db/init-db.js"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "pg": "^8.16.0"}, "devDependencies": {"nodemon": "^3.1.10"}}