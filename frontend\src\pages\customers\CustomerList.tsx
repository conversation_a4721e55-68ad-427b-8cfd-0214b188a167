import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { FaPlus, FaEdit, FaTrash, FaEye } from 'react-icons/fa';
import '../../styles/SharedStyles.css';

interface Customer {
  id: number;
  name: string;
  contact: string;
  address: string;
  notes: string;
  balance: number;
  created_at: string;
}

const CustomerList = () => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        const response = await axios.get('http://localhost:5000/api/customers');
        setCustomers(response.data);
        setLoading(false);
      } catch (err) {
        setError('Failed to fetch customers. Please try again later.');
        setLoading(false);
        console.error('Error fetching customers:', err);
      }
    };

    fetchCustomers();
  }, []);

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this customer?')) {
      try {
        await axios.delete(`http://localhost:5000/api/customers/${id}`);
        setCustomers(customers.filter(customer => customer.id !== id));
      } catch (err) {
        setError('Failed to delete customer. Please try again later.');
        console.error('Error deleting customer:', err);
      }
    }
  };

  if (loading) {
    return <div className="app-loading">Loading customers...</div>;
  }

  if (error) {
    return <div className="app-error">{error}</div>;
  }

  return (
    <div className="app-form-container">
      <div className="app-header">
        <h1>Customers</h1>
        <div className="app-header-actions">
          <Link to="/customers/new" className="app-btn app-btn-primary">
            <FaPlus /> Add Customer
          </Link>
        </div>
      </div>

      {customers.length === 0 ? (
        <div className="app-card" style={{ textAlign: 'center', padding: '3rem' }}>
          <p style={{ fontSize: '1.1rem', color: '#666' }}>No customers found. Start by adding a customer.</p>
          <Link to="/customers/new" className="app-btn app-btn-primary" style={{ marginTop: '1rem' }}>
            <FaPlus /> Add Your First Customer
          </Link>
        </div>
      ) : (
        <div className="app-card">
          <table className="app-table">
            <thead>
              <tr>
                <th>Name</th>
                <th>Contact</th>
                <th>Balance</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {customers.map(customer => (
                <tr key={customer.id}>
                  <td><strong>{customer.name}</strong></td>
                  <td>{customer.contact || 'Not provided'}</td>
                  <td>
                    <span style={{
                      color: parseFloat(customer.balance || '0') >= 0 ? '#2ecc71' : '#e74c3c',
                      fontWeight: 600
                    }}>
                      PKR {typeof customer.balance === 'number' ? customer.balance.toFixed(2) : parseFloat(customer.balance || '0').toFixed(2)}
                    </span>
                  </td>
                  <td className="app-table-actions">
                    <Link to={`/customers/${customer.id}`} className="app-btn app-btn-icon" title="View Details">
                      <FaEye />
                    </Link>
                    <Link to={`/customers/edit/${customer.id}`} className="app-btn app-btn-icon app-btn-primary" title="Edit">
                      <FaEdit />
                    </Link>
                    <button
                      onClick={() => handleDelete(customer.id)}
                      className="app-btn app-btn-icon app-btn-danger"
                      title="Delete"
                    >
                      <FaTrash />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default CustomerList;
