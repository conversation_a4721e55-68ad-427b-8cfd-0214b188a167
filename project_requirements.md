# Detailed Project Requirements

## 1. Product Management
- **Add Products**:
  - Product name (required)
  - Product description (optional)
  - Product ID (auto-generated)
- **View Products**:
  - List view with search and filter capabilities
  - Detailed view showing production history and current stock

## 2. Batch-wise Expense Management
- **Batch Creation**:
  - Batch number (auto-generated or manual)
  - Production date
  - Product selection
  - Quantity produced
- **Raw Material Entry**:
  - Multiple materials per batch
  - For each material:
    - Material name
    - Quantity used
    - Rate per unit
    - Total cost (auto-calculated)
- **Cost Calculations**:
  - Total batch cost (sum of all raw materials)
  - Cost per unit (total cost ÷ quantity produced)
  - Historical cost tracking for trend analysis

## 3. Stock Management
- **Stock Updates**:
  - Automatic addition after batch production
  - Automatic reduction after sales
- **Stock Monitoring**:
  - Current stock levels for each product
  - Low stock alerts (optional enhancement)
  - Stock history and movement tracking

## 4. Vendor <PERSON>a (Ledger)
- **Vendor Management**:
  - Vendor name
  - Contact information
  - Address (optional)
  - Notes (optional)
- **Purchase Tracking**:
  - Date of purchase
  - Materials purchased
  - Quantity
  - Rate
  - Total amount
  - Payment status (paid/partial/unpaid)
- **Financial Tracking**:
  - Total purchases from vendor
  - Total payments made
  - Outstanding balance
  - Payment history

## 5. Customer Khata (Ledger)
- **Customer Management**:
  - Customer name
  - Contact information
  - Address (optional)
  - Notes (optional)
- **Sales Tracking**:
  - Date of sale
  - Products sold
  - Quantity
  - Rate
  - Total amount
  - Payment status (paid/partial/unpaid)
- **Financial Tracking**:
  - Total sales to customer
  - Total payments received
  - Outstanding balance
  - Payment history

## 6. Sales System
- **Sales Entry**:
  - Customer selection
  - Product selection (with available stock display)
  - Quantity (with validation against available stock)
  - Sale price per unit
  - Total amount (auto-calculated)
  - Payment amount received
  - Balance amount (auto-calculated)
- **Sales Processing**:
  - Stock reduction
  - Customer khata update
  - Profit calculation (optional display)

## 7. Reports
- **Production Reports**:
  - Batch-wise production details
  - Cost analysis per batch and product
- **Inventory Reports**:
  - Current stock levels
  - Stock movement history
- **Financial Reports**:
  - Vendor-wise outstanding amounts
  - Customer-wise outstanding amounts
  - Daily/monthly sales summaries
  - Profit margins by product/batch
- **Export Options**:
  - PDF export
  - Excel/CSV export

## UI/UX Requirements
- Simple, intuitive interface suitable for non-technical users
- Responsive design for desktop use
- Clear navigation between modules
- Consistent design language
- Form validation with clear error messages
- Confirmation dialogs for critical actions

## Data Management
- CRUD operations for all entities
- Data validation
- Audit trails for critical operations (optional)
- Data backup mechanisms
