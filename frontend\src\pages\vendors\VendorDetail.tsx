import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import axios from 'axios';
import { FaEdit, FaArrowLeft, FaPlus, FaMoneyBillWave, FaBoxOpen, FaUser } from 'react-icons/fa';
import '../../styles/SharedStyles.css';

interface Transaction {
  id: number;
  transaction_date: string;
  material_name: string;
  quantity: number;
  rate: number;
  total_amount: number;
  paid_amount: number;
  payment_status: string;
  notes: string;
  created_at: string;
  updated_at: string;
}

interface Vendor {
  id: number;
  name: string;
  contact: string;
  address: string;
  notes: string;
  balance: number;
  created_at: string;
  transactions: Transaction[];
}

const VendorDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [vendor, setVendor] = useState<Vendor | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchVendor = async () => {
      try {
        const response = await axios.get(`http://localhost:5000/api/vendors/${id}`);
        setVendor(response.data);
        setLoading(false);
      } catch (err) {
        setError('Failed to fetch vendor details. Please try again later.');
        setLoading(false);
        console.error('Error fetching vendor:', err);
      }
    };

    fetchVendor();
  }, [id]);

  if (loading) {
    return <div className="app-loading">Loading vendor details...</div>;
  }

  if (error) {
    return <div className="app-error">{error}</div>;
  }

  if (!vendor) {
    return <div className="app-error">Vendor not found</div>;
  }

  return (
    <div className="app-form-container">
      <div className="app-header">
        <h1>
          <FaUser style={{ marginRight: '0.5rem', color: '#3498db' }} /> {vendor.name}
        </h1>
        <div className="app-header-actions">
          <Link to="/vendors" className="app-btn app-btn-sm">
            <FaArrowLeft /> Back to Vendors
          </Link>
          <Link to={`/vendors/edit/${vendor.id}`} className="app-btn app-btn-sm app-btn-primary">
            <FaEdit /> Edit Vendor
          </Link>
          <Link to={`/vendors/${vendor.id}/transactions/new`} className="app-btn app-btn-sm app-btn-secondary">
            <FaBoxOpen /> Add Purchase
          </Link>
          <Link to={`/vendors/${vendor.id}/payments/new`} className="app-btn app-btn-sm app-btn-success">
            <FaMoneyBillWave /> Add Payment
          </Link>
        </div>
      </div>

      <div className="app-card">
        <h2 className="app-section-header">Vendor Information</h2>

        <div className="app-form-row">
          <div className="app-form-col">
            <div className="app-form-group">
              <label className="app-form-label">Contact</label>
              <div className="app-form-value">{vendor.contact || 'Not provided'}</div>
            </div>
          </div>

          <div className="app-form-col">
            <div className="app-form-group">
              <label className="app-form-label">Current Balance</label>
              <div className="app-form-value" style={{
                color: parseFloat(vendor.balance || '0') > 0 ? '#e74c3c' : '#2ecc71',
                fontWeight: 600
              }}>
                PKR {typeof vendor.balance === 'number' ? vendor.balance.toFixed(2) : '0.00'}
              </div>
            </div>
          </div>

          <div className="app-form-col">
            <div className="app-form-group">
              <label className="app-form-label">Created At</label>
              <div className="app-form-value">{new Date(vendor.created_at).toLocaleDateString()}</div>
            </div>
          </div>
        </div>

        <div className="app-form-group">
          <label className="app-form-label">Address</label>
          <div className="app-form-value">{vendor.address || 'Not provided'}</div>
        </div>

        <div className="app-form-group">
          <label className="app-form-label">Notes</label>
          <div className="app-form-value">{vendor.notes || 'Not provided'}</div>
        </div>
      </div>

      <div className="app-card">
        <h2 className="app-section-header">
          <FaBoxOpen style={{ marginRight: '0.5rem' }} /> Purchase History
        </h2>

        {vendor.transactions && vendor.transactions.length > 0 ? (
          <table className="app-table">
            <thead>
              <tr>
                <th>Date</th>
                <th>Material</th>
                <th>Quantity</th>
                <th>Rate</th>
                <th>Total Amount</th>
                <th>Paid Amount</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              {vendor.transactions.map(transaction => (
                <tr
                  key={transaction.id}
                  style={transaction.material_name === 'Payment' ? { backgroundColor: '#f8f9fa' } : {}}
                >
                  <td>{new Date(transaction.transaction_date).toLocaleDateString()}</td>
                  <td>
                    {transaction.material_name === 'Payment' ? (
                      <strong style={{ color: '#2ecc71' }}>Payment</strong>
                    ) : (
                      <strong>{transaction.material_name}</strong>
                    )}
                  </td>
                  <td>
                    {transaction.material_name === 'Payment' ? (
                      '-'
                    ) : (
                      transaction.quantity
                    )}
                  </td>
                  <td>
                    {transaction.material_name === 'Payment' ? (
                      '-'
                    ) : (
                      `PKR ${parseFloat(String(transaction.rate || 0)).toFixed(2)}`
                    )}
                  </td>
                  <td>
                    {transaction.material_name === 'Payment' ? (
                      '-'
                    ) : (
                      <span style={{ color: '#3498db', fontWeight: 600 }}>
                        PKR {parseFloat(String(transaction.total_amount || 0)).toFixed(2)}
                      </span>
                    )}
                  </td>
                  <td>
                    {transaction.material_name === 'Payment' ? (
                      <span style={{ color: '#2ecc71', fontWeight: 600 }}>
                        PKR {parseFloat(String(transaction.paid_amount || 0)).toFixed(2)}
                      </span>
                    ) : (
                      <span>
                        PKR {parseFloat(String(transaction.paid_amount || 0)).toFixed(2)}
                      </span>
                    )}
                  </td>
                  <td>
                    {transaction.material_name === 'Payment' ? (
                      <span className="app-badge app-badge-success">Payment Made</span>
                    ) : (
                      <span className={`app-badge ${
                        transaction.payment_status === 'paid'
                          ? 'app-badge-success'
                          : transaction.payment_status === 'partial'
                            ? 'app-badge-warning'
                            : 'app-badge-danger'
                      }`}>
                        {transaction.payment_status}
                      </span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <p style={{ textAlign: 'center', padding: '2rem', color: '#666' }}>
            No purchase history found for this vendor.
          </p>
        )}
      </div>
    </div>
  );
};

export default VendorDetail;
