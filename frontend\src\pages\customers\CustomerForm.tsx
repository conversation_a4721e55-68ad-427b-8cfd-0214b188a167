import { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { FaArrowLeft, FaSave } from 'react-icons/fa';
import '../../styles/SharedStyles.css';

interface CustomerFormData {
  name: string;
  contact: string;
  address: string;
  notes: string;
}

const CustomerForm = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEditMode = !!id;

  const [formData, setFormData] = useState<CustomerFormData>({
    name: '',
    contact: '',
    address: '',
    notes: ''
  });
  const [loading, setLoading] = useState<boolean>(isEditMode);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    const fetchCustomer = async () => {
      if (isEditMode) {
        try {
          const response = await axios.get(`http://localhost:5000/api/customers/${id}`);
          const { name, contact, address, notes } = response.data;
          setFormData({ name, contact, address, notes });
          setLoading(false);
        } catch (err) {
          setError('Failed to fetch customer details. Please try again later.');
          setLoading(false);
          console.error('Error fetching customer:', err);
        }
      }
    };

    fetchCustomer();
  }, [id, isEditMode]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      if (isEditMode) {
        await axios.put(`http://localhost:5000/api/customers/${id}`, formData);
        setSuccess('Customer updated successfully!');
        setTimeout(() => navigate(`/customers/${id}`), 1500);
      } else {
        const response = await axios.post('http://localhost:5000/api/customers', formData);
        setSuccess('Customer created successfully!');
        setTimeout(() => navigate(`/customers/${response.data.id}`), 1500);
      }
    } catch (err) {
      setError('Failed to save customer. Please try again later.');
      console.error('Error saving customer:', err);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return <div className="app-loading">Loading customer details...</div>;
  }

  return (
    <div className="app-form-container">
      <div className="app-header">
        <h1>{isEditMode ? 'Edit Customer' : 'Add New Customer'}</h1>
        <div className="app-header-actions">
          <Link to="/customers" className="app-btn app-btn-sm">
            <FaArrowLeft /> Back to Customers
          </Link>
        </div>
      </div>

      <div className="app-card">
        {error && <div className="app-error">{error}</div>}
        {success && <div className="app-success">{success}</div>}

        <form onSubmit={handleSubmit}>
          <div className="app-form-row">
            <div className="app-form-col">
              <div className="app-form-group">
                <label htmlFor="name" className="app-form-label">Customer Name</label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  className="app-form-control"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Enter customer name"
                  required
                />
              </div>
            </div>

            <div className="app-form-col">
              <div className="app-form-group">
                <label htmlFor="contact" className="app-form-label">Contact Information (Phone/Email)</label>
                <input
                  type="text"
                  id="contact"
                  name="contact"
                  className="app-form-control"
                  value={formData.contact}
                  onChange={handleChange}
                  placeholder="Enter contact information"
                />
              </div>
            </div>
          </div>

          <div className="app-form-group">
            <label htmlFor="address" className="app-form-label">Address</label>
            <textarea
              id="address"
              name="address"
              className="app-form-control"
              value={formData.address}
              onChange={handleChange}
              placeholder="Enter customer address"
              rows={3}
            />
          </div>

          <div className="app-form-group">
            <label htmlFor="notes" className="app-form-label">Notes</label>
            <textarea
              id="notes"
              name="notes"
              className="app-form-control"
              value={formData.notes}
              onChange={handleChange}
              placeholder="Enter any additional notes"
              rows={3}
            />
          </div>

          <div style={{ textAlign: 'center', marginTop: '2rem' }}>
            <button
              type="submit"
              className="app-btn app-btn-primary"
              disabled={submitting}
              style={{ minWidth: '200px' }}
            >
              <FaSave /> {submitting ? 'Saving...' : 'Save Customer'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CustomerForm;
