import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { FaPlus, FaEdit, FaTrash, FaEye } from 'react-icons/fa';
import '../../styles/SharedStyles.css';

interface Vendor {
  id: number;
  name: string;
  contact: string;
  address: string;
  notes: string;
  balance: number;
  created_at: string;
}

const VendorList = () => {
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchVendors = async () => {
      try {
        const response = await axios.get('http://localhost:5000/api/vendors');
        setVendors(response.data);
        setLoading(false);
      } catch (err) {
        setError('Failed to fetch vendors. Please try again later.');
        setLoading(false);
        console.error('Error fetching vendors:', err);
      }
    };

    fetchVendors();
  }, []);

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this vendor?')) {
      try {
        await axios.delete(`http://localhost:5000/api/vendors/${id}`);
        setVendors(vendors.filter(vendor => vendor.id !== id));
      } catch (err) {
        setError('Failed to delete vendor. Please try again later.');
        console.error('Error deleting vendor:', err);
      }
    }
  };

  if (loading) {
    return <div className="app-loading">Loading vendors...</div>;
  }

  if (error) {
    return <div className="app-error">{error}</div>;
  }

  return (
    <div className="app-form-container">
      <div className="app-header">
        <h1>Vendors</h1>
        <div className="app-header-actions">
          <Link to="/vendors/new" className="app-btn app-btn-primary">
            <FaPlus /> Add Vendor
          </Link>
        </div>
      </div>

      {vendors.length === 0 ? (
        <div className="app-card" style={{ textAlign: 'center', padding: '3rem' }}>
          <p style={{ fontSize: '1.1rem', color: '#666' }}>No vendors found. Start by adding a vendor.</p>
          <Link to="/vendors/new" className="app-btn app-btn-primary" style={{ marginTop: '1rem' }}>
            <FaPlus /> Add Your First Vendor
          </Link>
        </div>
      ) : (
        <div className="app-card">
          <table className="app-table">
            <thead>
              <tr>
                <th>Name</th>
                <th>Contact</th>
                <th>Balance</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {vendors.map(vendor => (
                <tr key={vendor.id}>
                  <td><strong>{vendor.name}</strong></td>
                  <td>{vendor.contact || 'Not provided'}</td>
                  <td>
                    <span style={{
                      color: parseFloat(vendor.balance || '0') >= 0 ? '#2ecc71' : '#e74c3c',
                      fontWeight: 600
                    }}>
                      PKR {typeof vendor.balance === 'number' ? vendor.balance.toFixed(2) : parseFloat(vendor.balance || '0').toFixed(2)}
                    </span>
                  </td>
                  <td className="app-table-actions">
                    <Link to={`/vendors/${vendor.id}`} className="app-btn app-btn-icon" title="View Details">
                      <FaEye />
                    </Link>
                    <Link to={`/vendors/edit/${vendor.id}`} className="app-btn app-btn-icon app-btn-primary" title="Edit">
                      <FaEdit />
                    </Link>
                    <button
                      onClick={() => handleDelete(vendor.id)}
                      className="app-btn app-btn-icon app-btn-danger"
                      title="Delete"
                    >
                      <FaTrash />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default VendorList;
