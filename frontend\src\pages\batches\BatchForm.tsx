import React, { useState, useEffect } from 'react';
import { useNavigate, Link, useParams } from 'react-router-dom';
import axios from 'axios';
import { FaArrowLeft, FaSave, FaPlus, FaTrash } from 'react-icons/fa';
import './BatchStyles.css';

interface Product {
  id: number;
  name: string;
}

interface Vendor {
  id: number;
  name: string;
}

interface MaterialInput {
  id: string;
  material_name: string;
  total_cost: number;
  vendor_id?: number;
  // Keep these for database compatibility, but they won't be used in the form
  quantity?: number;
  rate?: number;
}

interface BatchFormData {
  product_id: number;
  batch_number: string;
  quantity_produced: number;
  production_date: string;
  materials: MaterialInput[];
}

const BatchForm = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = !!id;

  const [products, setProducts] = useState<Product[]>([]);
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [formData, setFormData] = useState<BatchFormData>({
    product_id: 0,
    batch_number: `B-${new Date().getTime().toString().slice(-6)}`,
    quantity_produced: 0,
    production_date: new Date().toISOString().split('T')[0],
    materials: [{
      id: Date.now().toString(),
      material_name: '',
      total_cost: 0,
      vendor_id: 0,
      quantity: 1, // Default value
      rate: 0 // Default value
    }]
  });
  const [loading, setLoading] = useState<boolean>(true);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch products and vendors in parallel
        const [productsRes, vendorsRes] = await Promise.all([
          axios.get('http://localhost:5000/api/products'),
          axios.get('http://localhost:5000/api/vendors')
        ]);

        setProducts(productsRes.data);
        setVendors(vendorsRes.data);

        // If in edit mode, fetch the batch data
        if (isEditMode && id) {
          const batchRes = await axios.get(`http://localhost:5000/api/batches/${id}`);
          const batchData = batchRes.data;

          // Transform the materials to include the id property needed for the form
          // and ensure total_cost is a number
          const materialsWithId = batchData.materials.map((material: any) => ({
            ...material,
            id: material.id.toString(), // Convert to string as our form expects string IDs
            total_cost: parseFloat(material.total_cost) || 0 // Ensure total_cost is a number
          }));

          setFormData({
            product_id: batchData.product_id,
            batch_number: batchData.batch_number,
            quantity_produced: batchData.quantity_produced,
            production_date: new Date(batchData.production_date).toISOString().split('T')[0],
            materials: materialsWithId
          });
        }

        setLoading(false);
      } catch (err) {
        setError('Failed to fetch data. Please try again later.');
        setLoading(false);
        console.error('Error fetching data:', err);
      }
    };

    fetchData();
  }, [id, isEditMode]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'product_id' || name === 'quantity_produced' ? parseInt(value) : value
    }));
  };

  const handleMaterialChange = (id: string, field: keyof MaterialInput, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      materials: prev.materials.map(material => {
        if (material.id === id) {
          // Just update the field directly
          return { ...material, [field]: value };
        }
        return material;
      })
    }));
  };

  const addMaterial = () => {
    setFormData(prev => ({
      ...prev,
      materials: [...prev.materials, {
        id: Date.now().toString(),
        material_name: '',
        total_cost: 0,
        vendor_id: 0,
        quantity: 1, // Default value
        rate: 0 // Default value
      }]
    }));
  };

  const removeMaterial = (id: string) => {
    if (formData.materials.length > 1) {
      setFormData(prev => ({
        ...prev,
        materials: prev.materials.filter(material => material.id !== id)
      }));
    }
  };

  const calculateTotalCost = () => {
    return formData.materials.reduce((total, material) => {
      // Ensure total_cost is treated as a number
      return total + (parseFloat(material.total_cost.toString()) || 0);
    }, 0);
  };

  const calculateCostPerUnit = () => {
    const totalCost = calculateTotalCost();
    return formData.quantity_produced > 0 ? totalCost / formData.quantity_produced : 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      // Set default values for quantity and rate for database compatibility
      const materialsWithDefaults = formData.materials.map(material => ({
        ...material,
        quantity: 1, // Default value
        rate: material.total_cost // Use total_cost as rate for simplicity
      }));

      if (isEditMode && id) {
        // Update existing batch
        await axios.put(`http://localhost:5000/api/batches/${id}`, {
          ...formData,
          materials: materialsWithDefaults,
          total_cost: calculateTotalCost(),
          cost_per_unit: calculateCostPerUnit()
        });
        setSuccess('Batch updated successfully!');
        setTimeout(() => navigate(`/batches/${id}`), 1500);
      } else {
        // Create new batch
        const response = await axios.post('http://localhost:5000/api/batches', {
          ...formData,
          materials: materialsWithDefaults,
          total_cost: calculateTotalCost(),
          cost_per_unit: calculateCostPerUnit()
        });
        setSuccess('Batch created successfully!');
        setTimeout(() => navigate(`/batches/${response.data.id}`), 1500);
      }
    } catch (err: any) {
      setError(`Failed to save batch: ${err.response?.data?.message || err.message || 'Unknown error'}`);
      console.error('Error saving batch:', err);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return <div className="batch-loading">Loading products...</div>;
  }

  return (
    <div className="batch-form-container">
      <div className="batch-header">
        <h1>{isEditMode ? 'Edit Production Batch' : 'Create New Production Batch'}</h1>
        <div className="batch-header-actions">
          <Link to="/batches" className="batch-btn batch-btn-sm">
            <FaArrowLeft /> Back to Batches
          </Link>
        </div>
      </div>

      <div className="batch-card">
        {error && <div className="batch-error">{error}</div>}
        {success && <div className="batch-success">{success}</div>}

        <form onSubmit={handleSubmit}>
          <div className="batch-form-row">
            <div className="batch-form-col">
              <div className="batch-form-group">
                <label htmlFor="product_id" className="batch-form-label">Product</label>
                <select
                  id="product_id"
                  name="product_id"
                  className="batch-form-control"
                  value={formData.product_id}
                  onChange={handleChange}
                  required
                >
                  <option value="">Select a product</option>
                  {products.map(product => (
                    <option key={product.id} value={product.id}>{product.name}</option>
                  ))}
                </select>
              </div>
            </div>

            <div className="batch-form-col">
              <div className="batch-form-group">
                <label htmlFor="batch_number" className="batch-form-label">Batch Number</label>
                <input
                  type="text"
                  id="batch_number"
                  name="batch_number"
                  className="batch-form-control"
                  value={formData.batch_number}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>
          </div>

          <div className="batch-form-row">
            <div className="batch-form-col">
              <div className="batch-form-group">
                <label htmlFor="quantity_produced" className="batch-form-label">Quantity Produced</label>
                <input
                  type="number"
                  id="quantity_produced"
                  name="quantity_produced"
                  className="batch-form-control"
                  value={formData.quantity_produced}
                  onChange={handleChange}
                  min="1"
                  required
                />
              </div>
            </div>

            <div className="batch-form-col">
              <div className="batch-form-group">
                <label htmlFor="production_date" className="batch-form-label">Production Date</label>
                <input
                  type="date"
                  id="production_date"
                  name="production_date"
                  className="batch-form-control"
                  value={formData.production_date}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>
          </div>

          <div className="batch-form-group">
            <h3>Materials Used</h3>
            {formData.materials.map((material, index) => (
              <div key={material.id} className="material-card">
                <div className="material-header">
                  <h4 className="material-title">Material #{index + 1}</h4>
                  <button
                    type="button"
                    className="batch-btn batch-btn-sm batch-btn-danger"
                    onClick={() => removeMaterial(material.id)}
                    disabled={formData.materials.length <= 1}
                  >
                    <FaTrash /> Remove
                  </button>
                </div>

                <div className="batch-form-row">
                  <div className="batch-form-col">
                    <div className="batch-form-group">
                      <label className="batch-form-label">Vendor</label>
                      <select
                        className="batch-form-control"
                        value={material.vendor_id || ''}
                        onChange={(e) => handleMaterialChange(material.id, 'vendor_id', parseInt(e.target.value))}
                        required
                      >
                        <option value="">Select a vendor</option>
                        {vendors.map(vendor => (
                          <option key={vendor.id} value={vendor.id}>{vendor.name}</option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div className="batch-form-col">
                    <div className="batch-form-group">
                      <label className="batch-form-label">Material Name</label>
                      <input
                        type="text"
                        className="batch-form-control"
                        value={material.material_name}
                        onChange={(e) => handleMaterialChange(material.id, 'material_name', e.target.value)}
                        required
                      />
                    </div>
                  </div>
                </div>

                <div className="batch-form-group">
                  <label className="batch-form-label">Total Cost (PKR)</label>
                  <input
                    type="number"
                    className="batch-form-control"
                    value={material.total_cost}
                    onChange={(e) => handleMaterialChange(material.id, 'total_cost', parseFloat(e.target.value))}
                    min="0.01"
                    step="0.01"
                    required
                  />
                </div>
              </div>
            ))}
          </div>

          <button
            type="button"
            className="batch-btn batch-btn-secondary"
            onClick={addMaterial}
            style={{ marginBottom: '1.5rem' }}
          >
            <FaPlus /> Add Material
          </button>

          <div className="cost-summary">
            <h3>Cost Summary</h3>
            <div className="cost-item">
              <span className="cost-label">Total Cost:</span>
              <span className="cost-value">PKR {Number(calculateTotalCost()).toFixed(2)}</span>
            </div>
            <div className="cost-item">
              <span className="cost-label">Cost Per Unit:</span>
              <span className="cost-value">PKR {Number(calculateCostPerUnit()).toFixed(2)}</span>
            </div>
          </div>

          <div style={{ textAlign: 'center', marginTop: '2rem' }}>
            <button
              type="submit"
              className="batch-btn batch-btn-primary"
              disabled={submitting}
              style={{ minWidth: '200px' }}
            >
              <FaSave /> {submitting ? 'Saving...' : isEditMode ? 'Update Batch' : 'Create Batch'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BatchForm;
