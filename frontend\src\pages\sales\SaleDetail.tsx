import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import axios from 'axios';
import { FaArrowLeft, FaMoneyBillWave, FaShoppingCart, FaFileInvoiceDollar } from 'react-icons/fa';
import '../../styles/SharedStyles.css';

interface SaleItem {
  id: number;
  product_name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  cost_price: number;
  profit: number;
}

interface Sale {
  id: number;
  invoice_number: string;
  customer_id: number;
  customer_name: string;
  sale_date: string;
  total_amount: number;
  paid_amount: number; // Changed from payment_received to paid_amount to match backend
  balance: number;
  profit: number;
  items: SaleItem[];
}

const SaleDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [sale, setSale] = useState<Sale | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showPaymentForm, setShowPaymentForm] = useState<boolean>(false);
  const [paymentAmount, setPaymentAmount] = useState<string>('');
  const [paymentDate, setPaymentDate] = useState<string>(new Date().toISOString().split('T')[0]);
  const [paymentNotes, setPaymentNotes] = useState<string>('');

  useEffect(() => {
    const fetchSale = async () => {
      try {
        const response = await axios.get(`http://localhost:5000/api/sales/${id}`);
        setSale(response.data);
        setLoading(false);
      } catch (err) {
        setError('Failed to fetch sale details. Please try again later.');
        setLoading(false);
        console.error('Error fetching sale:', err);
      }
    };

    fetchSale();
  }, [id]);

  const handleAddPayment = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!paymentAmount || parseFloat(paymentAmount) <= 0) {
      setError('Please enter a valid payment amount');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await axios.post(`http://localhost:5000/api/sales/${id}/payment`, {
        amount: paymentAmount,
        payment_date: paymentDate,
        notes: paymentNotes
      });

      setSale(response.data);
      setSuccess('Payment added successfully');
      setShowPaymentForm(false);
      setPaymentAmount('');
      setPaymentNotes('');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);

      setLoading(false);
    } catch (err) {
      setError('Failed to add payment. Please try again.');
      setLoading(false);
      console.error('Error adding payment:', err);
    }
  };

  if (loading) {
    return <div className="app-loading">Loading sale details...</div>;
  }

  if (error) {
    return <div className="app-error">{error}</div>;
  }

  if (!sale) {
    return <div className="app-error">Sale not found</div>;
  }

  return (
    <div className="app-form-container">
      <div className="app-header">
        <h1>
          <FaFileInvoiceDollar style={{ marginRight: '0.5rem', color: '#3498db' }} />
          Invoice #{sale.invoice_number}
        </h1>
        <div className="app-header-actions">
          <Link to="/sales" className="app-btn app-btn-sm">
            <FaArrowLeft /> Back to Sales
          </Link>
        </div>
      </div>

      {success && <div className="app-success">{success}</div>}
      {error && <div className="app-error">{error}</div>}

      <div className="app-card">
        <h2 className="app-section-header">Sale Information</h2>

        <div className="app-form-row">
          <div className="app-form-col">
            <div className="app-form-group">
              <label className="app-form-label">Customer</label>
              <div className="app-form-value">{sale.customer_name}</div>
            </div>
          </div>

          <div className="app-form-col">
            <div className="app-form-group">
              <label className="app-form-label">Date</label>
              <div className="app-form-value">{new Date(sale.sale_date).toLocaleDateString()}</div>
            </div>
          </div>
        </div>

        <div className="app-form-row">
          <div className="app-form-col">
            <div className="app-form-group">
              <label className="app-form-label">Total Amount</label>
              <div className="app-form-value" style={{ color: '#3498db', fontWeight: 600 }}>
                PKR {parseFloat(sale.total_amount || 0).toFixed(2)}
              </div>
            </div>
          </div>

          <div className="app-form-col">
            <div className="app-form-group">
              <label className="app-form-label">Payment Received</label>
              <div className="app-form-value" style={{ color: '#2ecc71', fontWeight: 600 }}>
                PKR {parseFloat(sale.paid_amount || 0).toFixed(2)}
              </div>
            </div>
          </div>

          <div className="app-form-col">
            <div className="app-form-group">
              <label className="app-form-label">Balance</label>
              <div className="app-form-value" style={{
                color: parseFloat(sale.balance || 0) > 0 ? '#e74c3c' : '#2ecc71',
                fontWeight: 600
              }}>
                PKR {parseFloat(sale.balance || 0).toFixed(2)}
              </div>
            </div>
          </div>

          <div className="app-form-col">
            <div className="app-form-group">
              <label className="app-form-label">Profit</label>
              <div className="app-form-value" style={{ color: '#2ecc71', fontWeight: 600 }}>
                PKR {parseFloat(sale.profit || 0).toFixed(2)}
              </div>
            </div>
          </div>
        </div>

        {parseFloat(sale.balance) > 0 && (
          <div style={{ marginTop: '1.5rem', textAlign: 'center' }}>
            {!showPaymentForm ? (
              <button
                className="app-btn app-btn-success"
                onClick={() => setShowPaymentForm(true)}
              >
                <FaMoneyBillWave style={{ marginRight: '0.5rem' }} /> Add Payment
              </button>
            ) : (
              <div className="app-card" style={{ margin: '1.5rem 0', padding: '1.5rem', backgroundColor: '#f8f9fa' }}>
                <h3 className="app-section-header">
                  <FaMoneyBillWave style={{ marginRight: '0.5rem', color: '#2ecc71' }} /> Add Payment
                </h3>

                <form onSubmit={handleAddPayment}>
                  <div className="app-form-row">
                    <div className="app-form-col">
                      <div className="app-form-group">
                        <label htmlFor="paymentAmount" className="app-form-label">Payment Amount (PKR)</label>
                        <input
                          type="number"
                          id="paymentAmount"
                          className="app-form-control"
                          value={paymentAmount}
                          onChange={(e) => setPaymentAmount(e.target.value)}
                          step="0.01"
                          min="0.01"
                          max={sale.balance}
                          required
                        />
                      </div>
                    </div>

                    <div className="app-form-col">
                      <div className="app-form-group">
                        <label htmlFor="paymentDate" className="app-form-label">Payment Date</label>
                        <input
                          type="date"
                          id="paymentDate"
                          className="app-form-control"
                          value={paymentDate}
                          onChange={(e) => setPaymentDate(e.target.value)}
                          required
                        />
                      </div>
                    </div>
                  </div>

                  <div className="app-form-group">
                    <label htmlFor="paymentNotes" className="app-form-label">Notes (Optional)</label>
                    <textarea
                      id="paymentNotes"
                      className="app-form-control"
                      value={paymentNotes}
                      onChange={(e) => setPaymentNotes(e.target.value)}
                      placeholder="Enter any additional notes"
                      rows={2}
                    />
                  </div>

                  <div style={{ display: 'flex', justifyContent: 'center', gap: '1rem', marginTop: '1.5rem' }}>
                    <button type="submit" className="app-btn app-btn-primary">
                      Submit Payment
                    </button>
                    <button
                      type="button"
                      className="app-btn app-btn-danger"
                      onClick={() => {
                        setShowPaymentForm(false);
                        setError(null);
                      }}
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="app-card">
        <h2 className="app-section-header">
          <FaShoppingCart style={{ marginRight: '0.5rem' }} /> Sale Items
        </h2>

        {sale.items && sale.items.length > 0 ? (
          <table className="app-table">
            <thead>
              <tr>
                <th>Product</th>
                <th>Quantity</th>
                <th>Unit Price</th>
                <th>Total Price</th>
                <th>Profit</th>
              </tr>
            </thead>
            <tbody>
              {sale.items.map(item => (
                <tr key={item.id}>
                  <td><strong>{item.product_name}</strong></td>
                  <td>{item.quantity}</td>
                  <td>PKR {parseFloat(item.unit_price || 0).toFixed(2)}</td>
                  <td><span style={{ color: '#3498db', fontWeight: 600 }}>PKR {parseFloat(item.total_price || 0).toFixed(2)}</span></td>
                  <td><span style={{ color: '#2ecc71', fontWeight: 600 }}>PKR {parseFloat(item.profit || 0).toFixed(2)}</span></td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <p style={{ textAlign: 'center', padding: '2rem', color: '#666' }}>
            No items found for this sale.
          </p>
        )}
      </div>
    </div>
  );
};

export default SaleDetail;
