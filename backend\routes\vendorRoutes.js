const express = require('express');
const router = express.Router();
const {
  getVendors,
  getVendorById,
  createVendor,
  updateVendor,
  deleteVendor,
  addVendorTransaction,
  getVendorTransactions,
  addVendorPayment
} = require('../controllers/vendorController');

// Routes for /api/vendors
router.route('/')
  .get(getVendors)
  .post(createVendor);

router.route('/:id')
  .get(getVendorById)
  .put(updateVendor)
  .delete(deleteVendor);

router.route('/:id/transactions')
  .get(getVendorTransactions)
  .post(addVendorTransaction);

router.route('/:id/payments')
  .post(addVendorPayment);

module.exports = router;
