import { Link, useLocation } from 'react-router-dom';
import { FaHome, FaBox, FaUsers, FaUserTie, FaShoppingCart, FaChartBar, FaClipboardList } from 'react-icons/fa';

const Sidebar = () => {
  const location = useLocation();

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(`${path}/`);
  };

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <h3>Menu</h3>
      </div>

      <div className="sidebar-content">
        <ul className="sidebar-nav">
          <li className="sidebar-item">
            <Link to="/" className={`sidebar-link ${isActive('/') ? 'active' : ''}`}>
              <span className="sidebar-icon"><FaHome /></span>
              <span className="sidebar-text">Dashboard</span>
            </Link>
          </li>

          <li className="sidebar-item">
            <Link to="/products" className={`sidebar-link ${isActive('/products') ? 'active' : ''}`}>
              <span className="sidebar-icon"><FaBox /></span>
              <span className="sidebar-text">Products</span>
            </Link>
          </li>

          <li className="sidebar-item">
            <Link to="/batches" className={`sidebar-link ${isActive('/batches') ? 'active' : ''}`}>
              <span className="sidebar-icon"><FaChartBar /></span>
              <span className="sidebar-text">Batches</span>
            </Link>
          </li>

          <li className="sidebar-item">
            <Link to="/vendors" className={`sidebar-link ${isActive('/vendors') ? 'active' : ''}`}>
              <span className="sidebar-icon"><FaUserTie /></span>
              <span className="sidebar-text">Vendors</span>
            </Link>
          </li>

          <li className="sidebar-item">
            <Link to="/customers" className={`sidebar-link ${isActive('/customers') ? 'active' : ''}`}>
              <span className="sidebar-icon"><FaUsers /></span>
              <span className="sidebar-text">Customers</span>
            </Link>
          </li>

          <li className="sidebar-item">
            <Link to="/sales" className={`sidebar-link ${isActive('/sales') ? 'active' : ''}`}>
              <span className="sidebar-icon"><FaShoppingCart /></span>
              <span className="sidebar-text">Sales</span>
            </Link>
          </li>
        </ul>
      </div>

      <div className="sidebar-footer">
        <div className="sidebar-item">
          <Link to="/reports" className={`sidebar-link ${isActive('/reports') ? 'active' : ''}`}>
            <span className="sidebar-icon"><FaClipboardList /></span>
            <span className="sidebar-text">Reports</span>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
