const db = require('../db/db');

// @desc    Get all products
// @route   GET /api/simple-products
// @access  Public
const getProducts = async (req, res) => {
  try {
    console.log('Getting all products');
    const result = await db.query('SELECT * FROM products ORDER BY name');
    console.log('Query result:', result.rows);
    res.status(200).json(result.rows);
  } catch (error) {
    console.error('Error fetching products:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// @desc    Create a product
// @route   POST /api/simple-products
// @access  Public
const createProduct = async (req, res) => {
  try {
    console.log('Creating product with data:', req.body);
    const { name, description } = req.body;
    
    if (!name) {
      return res.status(400).json({ message: 'Product name is required' });
    }
    
    console.log('Inserting product into database');
    const result = await db.query(
      'INSERT INTO products (name, description) VALUES ($1, $2) RETURNING *',
      [name, description]
    );
    
    console.log('Product created:', result.rows[0]);
    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating product:', error);
    console.error('Error stack:', error.stack);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

module.exports = {
  getProducts,
  createProduct
};
