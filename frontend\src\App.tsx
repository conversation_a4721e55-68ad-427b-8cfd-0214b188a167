import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import './App.css';

// Layout components
import Navbar from './components/layout/Navbar';
import Sidebar from './components/layout/Sidebar';
import Footer from './components/layout/Footer';

// Page components
import Dashboard from './pages/Dashboard';

// Product components
import ProductList from './pages/products/ProductList';
import ProductDetail from './pages/products/ProductDetail';
import ProductForm from './pages/products/ProductForm';

// Customer components
import CustomerList from './pages/customers/CustomerList';
import CustomerDetail from './pages/customers/CustomerDetail';
import CustomerForm from './pages/customers/CustomerForm';
import CustomerPaymentForm from './pages/customers/CustomerPaymentForm';

// Vendor components
import VendorList from './pages/vendors/VendorList';
import VendorDetail from './pages/vendors/VendorDetail';
import VendorForm from './pages/vendors/VendorForm';
import VendorTransactionForm from './pages/vendors/VendorTransactionForm';
import VendorPaymentForm from './pages/vendors/VendorPaymentForm';

// Batch components
import BatchList from './pages/batches/BatchList';
import BatchDetail from './pages/batches/BatchDetail';
import BatchForm from './pages/batches/BatchForm';

// Sale components
import SaleList from './pages/sales/SaleList';
import SaleDetail from './pages/sales/SaleDetail';
import SaleForm from './pages/sales/SaleForm';

function App() {
  return (
    <Router>
      <div className="app-container">
        <Navbar />
        <div className="main-content">
          <Sidebar />
          <div className="content-area">
            <Routes>
              {/* Dashboard */}
              <Route path="/" element={<Dashboard />} />

              {/* Product Routes */}
              <Route path="/products" element={<ProductList />} />
              <Route path="/products/new" element={<ProductForm />} />
              <Route path="/products/edit/:id" element={<ProductForm />} />
              <Route path="/products/:id" element={<ProductDetail />} />

              {/* Customer Routes */}
              <Route path="/customers" element={<CustomerList />} />
              <Route path="/customers/new" element={<CustomerForm />} />
              <Route path="/customers/edit/:id" element={<CustomerForm />} />
              <Route path="/customers/:id" element={<CustomerDetail />} />
              <Route path="/customers/:id/payments/new" element={<CustomerPaymentForm />} />

              {/* Vendor Routes */}
              <Route path="/vendors" element={<VendorList />} />
              <Route path="/vendors/new" element={<VendorForm />} />
              <Route path="/vendors/edit/:id" element={<VendorForm />} />
              <Route path="/vendors/:id" element={<VendorDetail />} />
              <Route path="/vendors/:id/transactions/new" element={<VendorTransactionForm />} />
              <Route path="/vendors/:id/payments/new" element={<VendorPaymentForm />} />

              {/* Batch Routes */}
              <Route path="/batches" element={<BatchList />} />
              <Route path="/batches/new" element={<BatchForm />} />
              <Route path="/batches/edit/:id" element={<BatchForm />} />
              <Route path="/batches/:id" element={<BatchDetail />} />

              {/* Sale Routes */}
              <Route path="/sales" element={<SaleList />} />
              <Route path="/sales/new" element={<SaleForm />} />
              <Route path="/sales/:id" element={<SaleDetail />} />
            </Routes>
          </div>
        </div>
        <Footer />
      </div>
    </Router>
  );
}

export default App
