import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import axios from 'axios';
import { FaEdit, FaArrowLeft, FaPlus, FaMoneyBillWave, FaUser, FaShoppingCart } from 'react-icons/fa';
import '../../styles/SharedStyles.css';

interface Transaction {
  id: number;
  transaction_date: string;
  amount: number;
  transaction_type: 'payment' | 'sale';
  notes: string;
  created_at: string;
  updated_at: string;
}

interface Customer {
  id: number;
  name: string;
  contact: string;
  address: string;
  notes: string;
  balance: number;
  created_at: string;
  transactions: Transaction[];
  summary?: {
    total_sales: number;
    total_paid: number;
    balance: number;
  };
}

const CustomerDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCustomer = async () => {
      try {
        const response = await axios.get(`http://localhost:5000/api/customers/${id}`);
        setCustomer(response.data);
        setLoading(false);
      } catch (err) {
        setError('Failed to fetch customer details. Please try again later.');
        setLoading(false);
        console.error('Error fetching customer:', err);
      }
    };

    fetchCustomer();
  }, [id]);

  if (loading) {
    return <div className="app-loading">Loading customer details...</div>;
  }

  if (error) {
    return <div className="app-error">{error}</div>;
  }

  if (!customer) {
    return <div className="app-error">Customer not found</div>;
  }

  return (
    <div className="app-form-container">
      <div className="app-header">
        <h1>
          <FaUser style={{ marginRight: '0.5rem', color: '#3498db' }} /> {customer.name}
        </h1>
        <div className="app-header-actions">
          <Link to="/customers" className="app-btn app-btn-sm">
            <FaArrowLeft /> Back to Customers
          </Link>
          <Link to={`/customers/edit/${customer.id}`} className="app-btn app-btn-sm app-btn-primary">
            <FaEdit /> Edit Customer
          </Link>
          <Link to={`/customers/${customer.id}/payments/new`} className="app-btn app-btn-sm app-btn-success">
            <FaMoneyBillWave /> Add Payment
          </Link>
        </div>
      </div>

      <div className="app-card">
        <h2 className="app-section-header">Customer Information</h2>

        <div className="app-form-row">
          <div className="app-form-col">
            <div className="app-form-group">
              <label className="app-form-label">Contact</label>
              <div className="app-form-value">{customer.contact || 'Not provided'}</div>
            </div>
          </div>

          <div className="app-form-col">
            <div className="app-form-group">
              <label className="app-form-label">Current Balance</label>
              <div className="app-form-value" style={{
                color: parseFloat(customer.balance || '0') > 0 ? '#e74c3c' : '#2ecc71',
                fontWeight: 600
              }}>
                PKR {typeof customer.balance === 'number' ? customer.balance.toFixed(2) : '0.00'}
              </div>
            </div>
          </div>

          <div className="app-form-col">
            <div className="app-form-group">
              <label className="app-form-label">Created At</label>
              <div className="app-form-value">{new Date(customer.created_at).toLocaleDateString()}</div>
            </div>
          </div>
        </div>

        <div className="app-form-group">
          <label className="app-form-label">Address</label>
          <div className="app-form-value">{customer.address || 'Not provided'}</div>
        </div>

        <div className="app-form-group">
          <label className="app-form-label">Notes</label>
          <div className="app-form-value">{customer.notes || 'Not provided'}</div>
        </div>
      </div>

      <div className="app-card">
        <h2 className="app-section-header">
          <FaShoppingCart style={{ marginRight: '0.5rem' }} /> Transaction History
        </h2>

        {customer.transactions && customer.transactions.length > 0 ? (
          <table className="app-table">
            <thead>
              <tr>
                <th>Date</th>
                <th>Type</th>
                <th>Notes</th>
                <th>Amount</th>
              </tr>
            </thead>
            <tbody>
              {customer.transactions.map(transaction => (
                <tr
                  key={transaction.id}
                  style={transaction.transaction_type === 'payment' ? { backgroundColor: '#f8f9fa' } : {}}
                >
                  <td>{new Date(transaction.transaction_date).toLocaleDateString()}</td>
                  <td>
                    {transaction.transaction_type === 'payment' ? (
                      <span className="app-badge app-badge-success">Payment</span>
                    ) : (
                      <span className="app-badge app-badge-primary">Sale</span>
                    )}
                  </td>
                  <td>{transaction.notes}</td>
                  <td>
                    {transaction.transaction_type === 'payment' ? (
                      <span style={{ color: '#2ecc71', fontWeight: 600 }}>
                        PKR {parseFloat(String(transaction.amount || 0)).toFixed(2)}
                      </span>
                    ) : (
                      <span style={{ color: '#3498db', fontWeight: 600 }}>
                        PKR {parseFloat(String(transaction.amount || 0)).toFixed(2)}
                      </span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <p style={{ textAlign: 'center', padding: '2rem', color: '#666' }}>
            No transaction history found for this customer.
          </p>
        )}
      </div>
    </div>
  );
};

export default CustomerDetail;
