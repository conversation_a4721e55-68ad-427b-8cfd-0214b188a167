const db = require('../db/db');

// @desc    Get all vendors
// @route   GET /api/vendors
// @access  Public
const getVendors = async (req, res) => {
  try {
    // Get all vendors
    const vendorsResult = await db.query(`
      SELECT * FROM vendors ORDER BY name
    `);

    // For each vendor, calculate the correct summary
    const result = { rows: [] };

    for (const vendor of vendorsResult.rows) {
      // Get all transactions for this vendor
      const transactionsResult = await db.query(
        'SELECT * FROM vendor_transactions WHERE vendor_id = $1',
        [vendor.id]
      );

      // Calculate summary
      let total_purchases = 0;
      let total_paid = 0;

      transactionsResult.rows.forEach(transaction => {
        if (transaction.material_name === 'Payment') {
          // For payment transactions, add the paid_amount to total_paid
          total_paid += parseFloat(transaction.paid_amount);
        } else {
          // For purchase transactions, add the total_amount to total_purchases
          total_purchases += parseFloat(transaction.total_amount);
        }
      });

      const balance = total_purchases - total_paid;

      // Add vendor with calculated summary to result
      result.rows.push({
        ...vendor,
        total_purchases,
        total_paid,
        balance
      });
    }

    res.status(200).json(result.rows);
  } catch (error) {
    console.error('Error fetching vendors:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get single vendor
// @route   GET /api/vendors/:id
// @access  Public
const getVendorById = async (req, res) => {
  try {
    const { id } = req.params;

    // Get vendor details
    const vendorResult = await db.query(
      'SELECT * FROM vendors WHERE id = $1',
      [id]
    );

    if (vendorResult.rows.length === 0) {
      return res.status(404).json({ message: 'Vendor not found' });
    }

    // Get vendor transactions
    const transactionsResult = await db.query(
      'SELECT * FROM vendor_transactions WHERE vendor_id = $1 ORDER BY transaction_date DESC',
      [id]
    );

    // Calculate summary
    const summary = {
      total_purchases: 0,
      total_paid: 0,
      balance: 0
    };

    transactionsResult.rows.forEach(transaction => {
      if (transaction.material_name === 'Payment') {
        // For payment transactions, add the paid_amount to total_paid
        summary.total_paid += parseFloat(transaction.paid_amount);
      } else {
        // For purchase transactions, add the total_amount to total_purchases
        summary.total_purchases += parseFloat(transaction.total_amount);
      }
    });

    summary.balance = summary.total_purchases - summary.total_paid;

    res.status(200).json({
      ...vendorResult.rows[0],
      transactions: transactionsResult.rows,
      summary
    });
  } catch (error) {
    console.error('Error fetching vendor:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Create a vendor
// @route   POST /api/vendors
// @access  Public
const createVendor = async (req, res) => {
  try {
    const { name, contact, address, notes } = req.body;

    if (!name) {
      return res.status(400).json({ message: 'Vendor name is required' });
    }

    const result = await db.query(
      'INSERT INTO vendors (name, contact, address, notes) VALUES ($1, $2, $3, $4) RETURNING *',
      [name, contact, address, notes]
    );

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating vendor:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Update a vendor
// @route   PUT /api/vendors/:id
// @access  Public
const updateVendor = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, contact, address, notes } = req.body;

    if (!name) {
      return res.status(400).json({ message: 'Vendor name is required' });
    }

    const result = await db.query(
      'UPDATE vendors SET name = $1, contact = $2, address = $3, notes = $4, updated_at = CURRENT_TIMESTAMP WHERE id = $5 RETURNING *',
      [name, contact, address, notes, id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Vendor not found' });
    }

    res.status(200).json(result.rows[0]);
  } catch (error) {
    console.error('Error updating vendor:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Delete a vendor
// @route   DELETE /api/vendors/:id
// @access  Public
const deleteVendor = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query(
      'DELETE FROM vendors WHERE id = $1 RETURNING *',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Vendor not found' });
    }

    res.status(200).json({ message: 'Vendor deleted successfully' });
  } catch (error) {
    console.error('Error deleting vendor:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Add a transaction for a vendor
// @route   POST /api/vendors/:id/transactions
// @access  Public
const addVendorTransaction = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      transaction_date,
      material_name,
      quantity,
      rate,
      total_amount,
      paid_amount,
      notes
    } = req.body;

    // Validate required fields
    if (!material_name || !quantity || !rate || !total_amount) {
      return res.status(400).json({
        message: 'Material name, quantity, rate, and total amount are required'
      });
    }

    // Check if vendor exists
    const vendorCheck = await db.query(
      'SELECT * FROM vendors WHERE id = $1',
      [id]
    );

    if (vendorCheck.rows.length === 0) {
      return res.status(404).json({ message: 'Vendor not found' });
    }

    // Determine payment status
    let payment_status = 'unpaid';
    if (parseFloat(paid_amount) >= parseFloat(total_amount)) {
      payment_status = 'paid';
    } else if (parseFloat(paid_amount) > 0) {
      payment_status = 'partial';
    }

    const result = await db.query(
      `INSERT INTO vendor_transactions
        (vendor_id, transaction_date, material_name, quantity, rate, total_amount, paid_amount, payment_status, notes)
       VALUES
        ($1, $2, $3, $4, $5, $6, $7, $8, $9)
       RETURNING *`,
      [
        id,
        transaction_date || new Date(),
        material_name,
        quantity,
        rate,
        total_amount,
        paid_amount || 0,
        payment_status,
        notes
      ]
    );

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error adding vendor transaction:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get all transactions for a vendor
// @route   GET /api/vendors/:id/transactions
// @access  Public
const getVendorTransactions = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query(
      'SELECT * FROM vendor_transactions WHERE vendor_id = $1 ORDER BY transaction_date DESC',
      [id]
    );

    res.status(200).json(result.rows);
  } catch (error) {
    console.error('Error fetching vendor transactions:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Add a payment for a vendor
// @route   POST /api/vendors/:id/payments
// @access  Public
const addVendorPayment = async (req, res) => {
  const client = await db.getClient();

  try {
    await client.query('BEGIN');

    const { id } = req.params;
    const { transaction_date, amount, notes, material_id } = req.body;

    // Validate required fields
    if (!amount || parseFloat(amount) <= 0) {
      return res.status(400).json({ message: 'Valid payment amount is required' });
    }

    // Check if vendor exists
    const vendorCheck = await client.query(
      'SELECT * FROM vendors WHERE id = $1',
      [id]
    );

    if (vendorCheck.rows.length === 0) {
      return res.status(404).json({ message: 'Vendor not found' });
    }

    // If material_id is provided, update that specific transaction
    if (material_id) {
      const transactionResult = await client.query(
        'SELECT * FROM vendor_transactions WHERE id = $1 AND vendor_id = $2',
        [material_id, id]
      );

      if (transactionResult.rows.length === 0) {
        return res.status(404).json({ message: 'Transaction not found' });
      }

      const transaction = transactionResult.rows[0];
      const newPaidAmount = parseFloat(transaction.paid_amount) + parseFloat(amount);
      let newPaymentStatus = 'partial';

      if (newPaidAmount >= parseFloat(transaction.total_amount)) {
        newPaymentStatus = 'paid';
      }

      await client.query(
        'UPDATE vendor_transactions SET paid_amount = $1, payment_status = $2, updated_at = CURRENT_TIMESTAMP WHERE id = $3',
        [newPaidAmount, newPaymentStatus, material_id]
      );
    }

    // Create a new transaction record with 'Payment' as material_name to indicate payment
    await client.query(
      `INSERT INTO vendor_transactions
        (vendor_id, transaction_date, material_name, quantity, rate, total_amount, paid_amount, payment_status, notes)
       VALUES
        ($1, $2, 'Payment', 0.01, 0.01, 0.01, $3, 'paid', $4)`,
      [
        id,
        transaction_date || new Date(),
        amount,
        notes || 'Vendor payment'
      ]
    );

    await client.query('COMMIT');

    // Get updated vendor data
    const vendorResult = await client.query(
      'SELECT * FROM vendors WHERE id = $1',
      [id]
    );

    const transactionsResult = await client.query(
      'SELECT * FROM vendor_transactions WHERE vendor_id = $1 ORDER BY transaction_date DESC',
      [id]
    );

    // Calculate summary
    const summary = {
      total_purchases: 0,
      total_paid: 0,
      balance: 0
    };

    transactionsResult.rows.forEach(transaction => {
      if (transaction.material_name === 'Payment') {
        // For payment transactions, add the paid_amount to total_paid
        summary.total_paid += parseFloat(transaction.paid_amount);
      } else {
        // For purchase transactions, add the total_amount to total_purchases
        // and the paid_amount to total_paid (for partial payments made during purchase)
        summary.total_purchases += parseFloat(transaction.total_amount);
      }
    });

    summary.balance = summary.total_purchases - summary.total_paid;

    res.status(200).json({
      ...vendorResult.rows[0],
      transactions: transactionsResult.rows,
      summary,
      message: 'Payment added successfully'
    });
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error adding vendor payment:', error);
    res.status(500).json({ message: 'Server error' });
  } finally {
    client.release();
  }
};

module.exports = {
  getVendors,
  getVendorById,
  createVendor,
  updateVendor,
  deleteVendor,
  addVendorTransaction,
  getVendorTransactions,
  addVendorPayment
};
