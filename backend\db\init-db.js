const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

dotenv.config();

const createDatabase = async () => {
  // Connect to postgres database to create our application database
  const pgPool = new Pool({
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    database: 'postgres', // Connect to default postgres database
  });

  try {
    // Check if our database already exists
    const checkDbResult = await pgPool.query(
      "SELECT 1 FROM pg_database WHERE datname = $1",
      [process.env.DB_NAME]
    );

    // If database doesn't exist, create it
    if (checkDbResult.rowCount === 0) {
      console.log(`Creating database: ${process.env.DB_NAME}`);
      await pgPool.query(`CREATE DATABASE ${process.env.DB_NAME}`);
      console.log(`Database ${process.env.DB_NAME} created successfully`);
    } else {
      console.log(`Database ${process.env.DB_NAME} already exists`);
    }
  } catch (err) {
    console.error('Error creating database:', err);
    throw err;
  } finally {
    await pgPool.end();
  }
};

const initSchema = async () => {
  // Connect to our application database
  const appPool = new Pool({
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    database: process.env.DB_NAME,
  });

  try {
    // Read schema SQL file
    const schemaPath = path.join(__dirname, 'schema.sql');
    const schemaSql = fs.readFileSync(schemaPath, 'utf8');

    // Execute schema SQL
    console.log('Initializing database schema...');
    await appPool.query(schemaSql);
    console.log('Database schema initialized successfully');
  } catch (err) {
    console.error('Error initializing schema:', err);
    throw err;
  } finally {
    await appPool.end();
  }
};

const initDb = async () => {
  try {
    await createDatabase();
    await initSchema();
    console.log('Database initialization completed successfully');
  } catch (err) {
    console.error('Database initialization failed:', err);
    process.exit(1);
  }
};

// Run the initialization
initDb();
