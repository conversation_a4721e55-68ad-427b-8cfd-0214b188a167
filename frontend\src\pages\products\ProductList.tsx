import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { FaPlus, FaEdit, FaTrash, FaEye } from 'react-icons/fa';
import '../../styles/SharedStyles.css';

interface Product {
  id: number;
  name: string;
  description: string;
  stock: number;
  created_at: string;
}

const ProductList = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const response = await axios.get('http://localhost:5000/api/products');
        setProducts(response.data);
        setLoading(false);
      } catch (err) {
        setError('Failed to fetch products. Please try again later.');
        setLoading(false);
        console.error('Error fetching products:', err);
      }
    };

    fetchProducts();
  }, []);

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this product?')) {
      try {
        await axios.delete(`http://localhost:5000/api/products/${id}`);
        setProducts(products.filter(product => product.id !== id));
      } catch (err) {
        setError('Failed to delete product. Please try again later.');
        console.error('Error deleting product:', err);
      }
    }
  };

  if (loading) {
    return <div className="app-loading">Loading products...</div>;
  }

  if (error) {
    return <div className="app-error">{error}</div>;
  }

  return (
    <div className="app-form-container">
      <div className="app-header">
        <h1>Products</h1>
        <div className="app-header-actions">
          <Link to="/products/new" className="app-btn app-btn-primary">
            <FaPlus /> Add Product
          </Link>
        </div>
      </div>

      {products.length === 0 ? (
        <div className="app-card" style={{ textAlign: 'center', padding: '3rem' }}>
          <p style={{ fontSize: '1.1rem', color: '#666' }}>No products found. Start by adding a product.</p>
          <Link to="/products/new" className="app-btn app-btn-primary" style={{ marginTop: '1rem' }}>
            <FaPlus /> Add Your First Product
          </Link>
        </div>
      ) : (
        <div className="app-card">
          <table className="app-table">
            <thead>
              <tr>
                <th>Name</th>
                <th>Description</th>
                <th>Current Stock</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {products.map(product => (
                <tr key={product.id}>
                  <td><strong>{product.name}</strong></td>
                  <td>{product.description || 'No description'}</td>
                  <td>
                    <span style={{
                      color: product.stock > 10 ? '#2ecc71' : product.stock > 0 ? '#f39c12' : '#e74c3c',
                      fontWeight: 600
                    }}>
                      {product.stock} units
                    </span>
                  </td>
                  <td className="app-table-actions">
                    <Link to={`/products/${product.id}`} className="app-btn app-btn-icon" title="View Details">
                      <FaEye />
                    </Link>
                    <Link to={`/products/edit/${product.id}`} className="app-btn app-btn-icon app-btn-primary" title="Edit">
                      <FaEdit />
                    </Link>
                    <button
                      onClick={() => handleDelete(product.id)}
                      className="app-btn app-btn-icon app-btn-danger"
                      title="Delete"
                    >
                      <FaTrash />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default ProductList;
