const express = require('express');
const router = express.Router();
const {
  getCustomers,
  getCustomerById,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  addCustomerPayment,
  getCustomerTransactions
} = require('../controllers/customerController');

// Routes for /api/customers
router.route('/')
  .get(getCustomers)
  .post(createCustomer);

router.route('/:id')
  .get(getCustomerById)
  .put(updateCustomer)
  .delete(deleteCustomer);

router.route('/:id/transactions')
  .get(getCustomerTransactions);

router.route('/:id/payments')
  .post(addCustomerPayment);

module.exports = router;
