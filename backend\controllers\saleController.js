const db = require('../db/db');

// @desc    Get all sales
// @route   GET /api/sales
// @access  Public
const getSales = async (req, res) => {
  try {
    const result = await db.query(`
      SELECT
        s.*,
        c.name as customer_name,
        (s.total_amount - s.paid_amount) as balance,
        (SELECT SUM(profit) FROM sale_items WHERE sale_id = s.id) as profit,
        json_agg(
          json_build_object(
            'id', si.id,
            'product_id', si.product_id,
            'product_name', p.name,
            'quantity', si.quantity,
            'unit_price', si.unit_price,
            'total_price', si.total_price,
            'profit', si.profit
          )
        ) as items
      FROM
        sales s
      LEFT JOIN
        customers c ON s.customer_id = c.id
      JOIN
        sale_items si ON s.id = si.sale_id
      JOIN
        products p ON si.product_id = p.id
      GROUP BY
        s.id, c.name
      ORDER BY
        s.sale_date DESC
    `);

    res.status(200).json(result.rows);
  } catch (error) {
    console.error('Error fetching sales:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get single sale
// @route   GET /api/sales/:id
// @access  Public
const getSaleById = async (req, res) => {
  try {
    const { id } = req.params;

    const saleResult = await db.query(`
      SELECT
        s.*,
        c.name as customer_name,
        (s.total_amount - s.paid_amount) as balance,
        (SELECT SUM(profit) FROM sale_items WHERE sale_id = s.id) as profit
      FROM
        sales s
      LEFT JOIN
        customers c ON s.customer_id = c.id
      WHERE
        s.id = $1
    `, [id]);

    if (saleResult.rows.length === 0) {
      return res.status(404).json({ message: 'Sale not found' });
    }

    const itemsResult = await db.query(`
      SELECT
        si.*,
        p.name as product_name
      FROM
        sale_items si
      JOIN
        products p ON si.product_id = p.id
      WHERE
        si.sale_id = $1
    `, [id]);

    res.status(200).json({
      ...saleResult.rows[0],
      items: itemsResult.rows
    });
  } catch (error) {
    console.error('Error fetching sale:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Create a sale
// @route   POST /api/sales
// @access  Public
const createSale = async (req, res) => {
  const client = await db.pool.connect();

  try {
    const {
      customer_id,
      sale_date,
      items,
      paid_amount,
      notes
    } = req.body;

    if (!items || !items.length) {
      return res.status(400).json({ message: 'Sale items are required' });
    }

    await client.query('BEGIN');

    // Calculate total amount
    let total_amount = 0;
    items.forEach(item => {
      total_amount += parseFloat(item.total_price);
    });

    // Determine payment status
    let payment_status = 'unpaid';
    if (parseFloat(paid_amount) >= parseFloat(total_amount)) {
      payment_status = 'paid';
    } else if (parseFloat(paid_amount) > 0) {
      payment_status = 'partial';
    }

    // Generate invoice number (INV-YYYYMMDD-ID)
    const today = new Date(sale_date || new Date());
    const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '');

    // Get the next ID from the sales sequence
    const seqResult = await client.query("SELECT nextval('sales_id_seq') as next_id");
    const nextId = seqResult.rows[0].next_id;

    // Format: INV-YYYYMMDD-ID
    const invoiceNumber = `INV-${dateStr}-${nextId}`;

    // Insert sale
    const saleResult = await client.query(
      `INSERT INTO sales
        (invoice_number, customer_id, sale_date, total_amount, paid_amount, payment_status, notes)
       VALUES
        ($1, $2, $3, $4, $5, $6, $7)
       RETURNING *`,
      [
        invoiceNumber,
        customer_id,
        sale_date || new Date(),
        total_amount,
        paid_amount || 0,
        payment_status,
        notes
      ]
    );

    const sale_id = saleResult.rows[0].id;

    // Process each item
    for (const item of items) {
      // Get current product cost
      const productResult = await client.query(
        'SELECT cost_per_unit FROM batches WHERE product_id = $1 ORDER BY production_date DESC LIMIT 1',
        [item.product_id]
      );

      const cost_price = productResult.rows.length > 0
        ? parseFloat(productResult.rows[0].cost_per_unit)
        : 0;

      const profit = (parseFloat(item.unit_price) - cost_price) * parseInt(item.quantity);

      // Insert sale item
      await client.query(
        `INSERT INTO sale_items
          (sale_id, product_id, quantity, unit_price, total_price, cost_price, profit)
         VALUES
          ($1, $2, $3, $4, $5, $6, $7)`,
        [
          sale_id,
          item.product_id,
          item.quantity,
          item.unit_price,
          item.total_price,
          cost_price,
          profit
        ]
      );

      // Update inventory
      await client.query(
        'UPDATE inventory SET quantity = quantity - $1, updated_at = CURRENT_TIMESTAMP WHERE product_id = $2',
        [item.quantity, item.product_id]
      );
    }

    // Add transaction to customer's account if customer_id is provided
    if (customer_id) {
      await client.query(
        `INSERT INTO customer_transactions
          (customer_id, transaction_date, amount, transaction_type, notes)
         VALUES
          ($1, $2, $3, 'sale', $4)`,
        [
          customer_id,
          sale_date || new Date(),
          total_amount,
          `Sale #${sale_id}`
        ]
      );

      // If payment was made, add payment transaction
      if (parseFloat(paid_amount) > 0) {
        await client.query(
          `INSERT INTO customer_transactions
            (customer_id, transaction_date, amount, transaction_type, notes)
           VALUES
            ($1, $2, $3, 'payment', $4)`,
          [
            customer_id,
            sale_date || new Date(),
            paid_amount,
            `Payment for Sale #${sale_id}`
          ]
        );
      }
    }

    await client.query('COMMIT');

    // Get the complete sale with items
    const completeSaleResult = await client.query(`
      SELECT
        s.*,
        c.name as customer_name,
        (s.total_amount - s.paid_amount) as balance,
        (SELECT SUM(profit) FROM sale_items WHERE sale_id = s.id) as profit
      FROM
        sales s
      LEFT JOIN
        customers c ON s.customer_id = c.id
      WHERE
        s.id = $1
    `, [sale_id]);

    const itemsResult = await client.query(`
      SELECT
        si.*,
        p.name as product_name
      FROM
        sale_items si
      JOIN
        products p ON si.product_id = p.id
      WHERE
        si.sale_id = $1
    `, [sale_id]);

    res.status(201).json({
      ...completeSaleResult.rows[0],
      items: itemsResult.rows
    });
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error creating sale:', error);
    res.status(500).json({ message: 'Server error' });
  } finally {
    client.release();
  }
};

// @desc    Delete a sale
// @route   DELETE /api/sales/:id
// @access  Public
const deleteSale = async (req, res) => {
  const client = await db.pool.connect();

  try {
    const { id } = req.params;

    await client.query('BEGIN');

    // Get sale details before deletion
    const saleResult = await client.query(
      'SELECT * FROM sales WHERE id = $1',
      [id]
    );

    if (saleResult.rows.length === 0) {
      await client.query('ROLLBACK');
      return res.status(404).json({ message: 'Sale not found' });
    }

    const sale = saleResult.rows[0];

    // Get sale items
    const itemsResult = await client.query(
      'SELECT * FROM sale_items WHERE sale_id = $1',
      [id]
    );

    // Restore inventory
    for (const item of itemsResult.rows) {
      await client.query(
        'UPDATE inventory SET quantity = quantity + $1, updated_at = CURRENT_TIMESTAMP WHERE product_id = $2',
        [item.quantity, item.product_id]
      );
    }

    // Delete customer transactions related to this sale if customer_id exists
    if (sale.customer_id) {
      await client.query(
        "DELETE FROM customer_transactions WHERE customer_id = $1 AND notes LIKE $2",
        [sale.customer_id, `%Sale #${id}%`]
      );

      await client.query(
        "DELETE FROM customer_transactions WHERE customer_id = $1 AND notes LIKE $2",
        [sale.customer_id, `%Payment for Sale #${id}%`]
      );
    }

    // Delete sale (will cascade to sale_items)
    await client.query(
      'DELETE FROM sales WHERE id = $1',
      [id]
    );

    await client.query('COMMIT');

    res.status(200).json({ message: 'Sale deleted successfully' });
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error deleting sale:', error);
    res.status(500).json({ message: 'Server error' });
  } finally {
    client.release();
  }
};

// @desc    Add payment to a sale
// @route   POST /api/sales/:id/payment
// @access  Public
const addPayment = async (req, res) => {
  const client = await db.pool.connect();

  try {
    const { id } = req.params;
    const { amount, payment_date, notes } = req.body;

    if (!amount || parseFloat(amount) <= 0) {
      return res.status(400).json({ message: 'Valid payment amount is required' });
    }

    await client.query('BEGIN');

    // Get sale details
    const saleResult = await client.query(
      'SELECT * FROM sales WHERE id = $1',
      [id]
    );

    if (saleResult.rows.length === 0) {
      await client.query('ROLLBACK');
      return res.status(404).json({ message: 'Sale not found' });
    }

    const sale = saleResult.rows[0];
    const newPaidAmount = parseFloat(sale.paid_amount) + parseFloat(amount);
    const newBalance = parseFloat(sale.total_amount) - newPaidAmount;

    // Determine new payment status
    let newPaymentStatus = 'unpaid';
    if (newPaidAmount >= parseFloat(sale.total_amount)) {
      newPaymentStatus = 'paid';
    } else if (newPaidAmount > 0) {
      newPaymentStatus = 'partial';
    }

    // Update sale with new payment information
    await client.query(
      'UPDATE sales SET paid_amount = $1, payment_status = $2, updated_at = CURRENT_TIMESTAMP WHERE id = $3',
      [newPaidAmount, newPaymentStatus, id]
    );

    // Add payment transaction to customer's account if customer_id exists
    if (sale.customer_id) {
      await client.query(
        `INSERT INTO customer_transactions
          (customer_id, transaction_date, amount, transaction_type, notes)
         VALUES
          ($1, $2, $3, 'payment', $4)`,
        [
          sale.customer_id,
          payment_date || new Date(),
          amount,
          notes || `Payment for Sale #${id}`
        ]
      );
    }

    await client.query('COMMIT');

    // Get the updated sale
    const updatedSaleResult = await client.query(`
      SELECT
        s.*,
        c.name as customer_name,
        (s.total_amount - s.paid_amount) as balance,
        (SELECT SUM(profit) FROM sale_items WHERE sale_id = s.id) as profit
      FROM
        sales s
      LEFT JOIN
        customers c ON s.customer_id = c.id
      WHERE
        s.id = $1
    `, [id]);

    const itemsResult = await client.query(`
      SELECT
        si.*,
        p.name as product_name
      FROM
        sale_items si
      JOIN
        products p ON si.product_id = p.id
      WHERE
        si.sale_id = $1
    `, [id]);

    res.status(200).json({
      ...updatedSaleResult.rows[0],
      items: itemsResult.rows,
      message: 'Payment added successfully'
    });
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error adding payment:', error);
    res.status(500).json({ message: 'Server error' });
  } finally {
    client.release();
  }
};

module.exports = {
  getSales,
  getSaleById,
  createSale,
  deleteSale,
  addPayment
};
