import { useState } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { FaArrowLeft, FaSave, FaBoxOpen } from 'react-icons/fa';
import '../../styles/SharedStyles.css';

interface TransactionFormData {
  transaction_date: string;
  material_name: string;
  quantity: number;
  rate: number;
  total_amount: number;
  paid_amount: number;
  notes: string;
}

const VendorTransactionForm = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [formData, setFormData] = useState<TransactionFormData>({
    transaction_date: new Date().toISOString().split('T')[0],
    material_name: '',
    quantity: 0,
    rate: 0,
    total_amount: 0,
    paid_amount: 0,
    notes: ''
  });
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    setFormData(prev => {
      const newData = { ...prev, [name]: value };

      // Auto-calculate total amount when quantity or rate changes
      if (name === 'quantity' || name === 'rate') {
        const quantity = name === 'quantity' ? parseFloat(value) || 0 : prev.quantity;
        const rate = name === 'rate' ? parseFloat(value) || 0 : prev.rate;
        newData.total_amount = quantity * rate;
      }

      return newData;
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      await axios.post(`http://localhost:5000/api/vendors/${id}/transactions`, formData);
      setSuccess('Transaction added successfully!');
      setTimeout(() => navigate(`/vendors/${id}`), 1500);
    } catch (err) {
      setError('Failed to add transaction. Please try again later.');
      console.error('Error adding transaction:', err);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="app-form-container">
      <div className="app-header">
        <h1>
          <FaBoxOpen style={{ marginRight: '0.5rem', color: '#3498db' }} /> Add Raw Material Purchase
        </h1>
        <div className="app-header-actions">
          <Link to={`/vendors/${id}`} className="app-btn app-btn-sm">
            <FaArrowLeft /> Back to Vendor
          </Link>
        </div>
      </div>

      <div className="app-card">
        {error && <div className="app-error">{error}</div>}
        {success && <div className="app-success">{success}</div>}

        <form onSubmit={handleSubmit}>
          <div className="app-form-row">
            <div className="app-form-col">
              <div className="app-form-group">
                <label htmlFor="transaction_date" className="app-form-label">Purchase Date</label>
                <input
                  type="date"
                  id="transaction_date"
                  name="transaction_date"
                  className="app-form-control"
                  value={formData.transaction_date}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>

            <div className="app-form-col">
              <div className="app-form-group">
                <label htmlFor="material_name" className="app-form-label">Material Name</label>
                <input
                  type="text"
                  id="material_name"
                  name="material_name"
                  className="app-form-control"
                  value={formData.material_name}
                  onChange={handleChange}
                  placeholder="Enter material name"
                  required
                />
              </div>
            </div>
          </div>

          <div className="app-form-row">
            <div className="app-form-col">
              <div className="app-form-group">
                <label htmlFor="quantity" className="app-form-label">Quantity</label>
                <input
                  type="number"
                  id="quantity"
                  name="quantity"
                  className="app-form-control"
                  value={formData.quantity}
                  onChange={handleChange}
                  min="0.01"
                  step="0.01"
                  required
                />
              </div>
            </div>

            <div className="app-form-col">
              <div className="app-form-group">
                <label htmlFor="rate" className="app-form-label">Rate (PKR per unit)</label>
                <input
                  type="number"
                  id="rate"
                  name="rate"
                  className="app-form-control"
                  value={formData.rate}
                  onChange={handleChange}
                  min="0.01"
                  step="0.01"
                  required
                />
              </div>
            </div>
          </div>

          <div className="app-form-row">
            <div className="app-form-col">
              <div className="app-form-group">
                <label htmlFor="total_amount" className="app-form-label">Total Amount (PKR)</label>
                <input
                  type="number"
                  id="total_amount"
                  name="total_amount"
                  className="app-form-control"
                  value={formData.total_amount}
                  onChange={handleChange}
                  min="0.01"
                  step="0.01"
                  required
                  readOnly
                  style={{ backgroundColor: '#f8f9fa' }}
                />
              </div>
            </div>

            <div className="app-form-col">
              <div className="app-form-group">
                <label htmlFor="paid_amount" className="app-form-label">Paid Amount (PKR)</label>
                <input
                  type="number"
                  id="paid_amount"
                  name="paid_amount"
                  className="app-form-control"
                  value={formData.paid_amount}
                  onChange={handleChange}
                  min="0"
                  step="0.01"
                  required
                />
              </div>
            </div>
          </div>

          <div className="app-form-group">
            <label htmlFor="notes" className="app-form-label">Notes</label>
            <textarea
              id="notes"
              name="notes"
              className="app-form-control"
              value={formData.notes}
              onChange={handleChange}
              placeholder="Enter any additional notes"
              rows={3}
            />
          </div>

          <div style={{ textAlign: 'center', marginTop: '2rem' }}>
            <button
              type="submit"
              className="app-btn app-btn-primary"
              disabled={submitting}
              style={{ minWidth: '200px' }}
            >
              <FaSave /> {submitting ? 'Saving...' : 'Save Transaction'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default VendorTransactionForm;
