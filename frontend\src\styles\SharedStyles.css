/* SharedStyles.css - Common styling for all components */

/* Common Card Styles */
.app-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 1.75rem;
  margin-bottom: 1.75rem;
  transition: all 0.3s ease;
}

.app-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* Header Styles */
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #eaeaea;
}

.app-header h1 {
  font-size: 1.75rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.app-header-actions {
  display: flex;
  gap: 12px;
}

/* Form Styles */
.app-form-container {
  max-width: 1200px;
  margin: 0 auto;
}

.app-form-group {
  margin-bottom: 1.5rem;
}

.app-form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.app-form-col {
  flex: 1;
  min-width: 250px;
}

.app-form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.95rem;
}

.app-form-control {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.app-form-control:focus {
  border-color: #3498db;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
  outline: none;
}

.app-form-control::placeholder {
  color: #aaa;
}

.app-form-value {
  padding: 0.75rem;
  background-color: #f8f9fa;
  border-radius: 8px;
  font-size: 1rem;
  min-height: 42px;
  display: flex;
  align-items: center;
}

/* Button Styles */
.app-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.6rem 1.2rem;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: 8px;
}

.app-btn-primary {
  background-color: #3498db;
  color: white;
}

.app-btn-primary:hover {
  background-color: #2980b9;
}

.app-btn-secondary {
  background-color: #2ecc71;
  color: white;
}

.app-btn-secondary:hover {
  background-color: #27ae60;
}

.app-btn-danger {
  background-color: #e74c3c;
  color: white;
}

.app-btn-danger:hover {
  background-color: #c0392b;
}

.app-btn-sm {
  padding: 0.4rem 0.8rem;
  font-size: 0.875rem;
}

.app-btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  padding: 0;
}

/* Table Styles */
.app-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 1.5rem;
}

.app-table th {
  background-color: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
  text-align: left;
  padding: 1rem;
  border-bottom: 2px solid #eaeaea;
}

.app-table td {
  padding: 1rem;
  border-bottom: 1px solid #eaeaea;
  vertical-align: middle;
}

.app-table tr:last-child td {
  border-bottom: none;
}

.app-table tr:hover td {
  background-color: #f8f9fa;
}

.app-table-actions {
  display: flex;
  gap: 8px;
}

/* Summary Card Styles */
.summary-card {
  background-color: #f1f8fe;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 1.5rem 0;
  border-left: 4px solid #3498db;
}

.summary-card h3 {
  margin-top: 0;
  color: #2c3e50;
  font-size: 1.2rem;
  margin-bottom: 1rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 1.1rem;
}

.summary-label {
  font-weight: 600;
  color: #2c3e50;
}

.summary-value {
  font-weight: 700;
  color: #3498db;
}

/* Loading and Error States */
.app-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 3rem;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.app-error {
  background-color: #fdf3f2;
  color: #e74c3c;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  border-left: 4px solid #e74c3c;
}

.app-success {
  background-color: #eafaf1;
  color: #27ae60;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  border-left: 4px solid #27ae60;
}

/* Section Styles */
.app-section {
  margin-bottom: 2rem;
}

.app-section-header {
  margin-bottom: 1.5rem;
  color: #2c3e50;
  border-bottom: 1px solid #eaeaea;
  padding-bottom: 0.75rem;
}

/* Status Badges */
.app-badge {
  display: inline-block;
  padding: 0.35em 0.65em;
  font-size: 0.75em;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
}

.app-badge-success {
  background-color: #d4edda;
  color: #155724;
}

.app-badge-warning {
  background-color: #fff3cd;
  color: #856404;
}

.app-badge-danger {
  background-color: #f8d7da;
  color: #721c24;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .app-form-row {
    flex-direction: column;
  }
  
  .app-form-col {
    width: 100%;
  }
  
  .app-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .app-header-actions {
    margin-top: 1rem;
  }
}
