import { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { FaArrowLeft, FaSave } from 'react-icons/fa';
import '../../styles/SharedStyles.css';

interface ProductFormData {
  name: string;
  description: string;
}

const ProductForm = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEditMode = !!id;

  const [formData, setFormData] = useState<ProductFormData>({
    name: '',
    description: ''
  });
  const [loading, setLoading] = useState<boolean>(isEditMode);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    const fetchProduct = async () => {
      if (isEditMode) {
        try {
          const response = await axios.get(`http://localhost:5000/api/products/${id}`);
          const { name, description } = response.data;
          setFormData({ name, description });
          setLoading(false);
        } catch (err) {
          setError('Failed to fetch product details. Please try again later.');
          setLoading(false);
          console.error('Error fetching product:', err);
        }
      }
    };

    fetchProduct();
  }, [id, isEditMode]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      if (isEditMode) {
        await axios.put(`http://localhost:5000/api/products/${id}`, formData);
        setSuccess('Product updated successfully!');
        setTimeout(() => navigate(`/products/${id}`), 1500);
      } else {
        const response = await axios.post('http://localhost:5000/api/products', formData);
        setSuccess('Product created successfully!');
        setTimeout(() => navigate(`/products/${response.data.id}`), 1500);
      }
    } catch (err) {
      setError('Failed to save product. Please try again later.');
      console.error('Error saving product:', err);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return <div className="app-loading">Loading product details...</div>;
  }

  return (
    <div className="app-form-container">
      <div className="app-header">
        <h1>{isEditMode ? 'Edit Product' : 'Add New Product'}</h1>
        <div className="app-header-actions">
          <Link to="/products" className="app-btn app-btn-sm">
            <FaArrowLeft /> Back to Products
          </Link>
        </div>
      </div>

      <div className="app-card">
        {error && <div className="app-error">{error}</div>}
        {success && <div className="app-success">{success}</div>}

        <form onSubmit={handleSubmit}>
          <div className="app-form-group">
            <label htmlFor="name" className="app-form-label">Product Name</label>
            <input
              type="text"
              id="name"
              name="name"
              className="app-form-control"
              value={formData.name}
              onChange={handleChange}
              placeholder="Enter product name"
              required
            />
          </div>

          <div className="app-form-group">
            <label htmlFor="description" className="app-form-label">Description</label>
            <textarea
              id="description"
              name="description"
              className="app-form-control"
              value={formData.description}
              onChange={handleChange}
              placeholder="Enter product description (optional)"
              rows={4}
            />
          </div>

          <div style={{ textAlign: 'center', marginTop: '2rem' }}>
            <button
              type="submit"
              className="app-btn app-btn-primary"
              disabled={submitting}
              style={{ minWidth: '200px' }}
            >
              <FaSave /> {submitting ? 'Saving...' : 'Save Product'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProductForm;
