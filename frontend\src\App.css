:root {
  --primary-color: #3498db;
  --secondary-color: #2ecc71;
  --accent-color: #f39c12;
  --danger-color: #e74c3c;
  --dark-color: #2c3e50;
  --light-color: #ecf0f1;
  --text-color: #333;
  --border-color: #ddd;
  --shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

  /* Dashboard Colors */
  --card-bg: #ffffff;
  --card-border-radius: 12px;
  --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  --dashboard-bg: #f8f9fa;

  /* Stat Card Colors */
  --products-color: #3498db;
  --vendors-color: #2ecc71;
  --customers-color: #9b59b6;
  --sales-color: #f39c12;

  /* Metric Card Colors */
  --profit-color: #27ae60;
  --receivables-color: #e74c3c;
  --payables-color: #8e44ad;
  --cashflow-color: #16a085;

  /* Transaction Colors */
  --sale-color: #3498db;
  --purchase-color: #2ecc71;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--dashboard-bg);
}

a {
  text-decoration: none;
  color: var(--primary-color);
}

ul {
  list-style: none;
}

.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.main-content {
  display: flex;
  flex: 1;
}

.content-area {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  background-color: var(--dashboard-bg);
}

/* Navbar Styles */
.navbar {
  background-color: white;
  color: var(--text-color);
  padding: 0.75rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
}

.navbar-brand {
  display: flex;
  align-items: center;
}

.navbar-brand a {
  display: flex;
  align-items: center;
  color: var(--primary-color);
  font-weight: 600;
  font-size: 1.25rem;
  text-decoration: none;
}

.brand-icon {
  margin-right: 0.75rem;
  font-size: 1.5rem;
}

.navbar-nav {
  display: flex;
  align-items: center;
}

.nav-item {
  margin-left: 1.5rem;
}

.nav-link {
  color: var(--text-color);
  font-weight: 500;
  padding: 0.5rem 0;
  position: relative;
  transition: color 0.2s;
}

.nav-link:hover {
  color: var(--primary-color);
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: width 0.2s;
}

.nav-link:hover::after {
  width: 100%;
}

/* Sidebar Styles */
.sidebar {
  width: 250px;
  background-color: white;
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  height: 100%;
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

.sidebar-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.sidebar-content {
  flex: 1;
  padding: 1rem 0;
  overflow-y: auto;
}

.sidebar-nav {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-item {
  margin-bottom: 0.25rem;
}

.sidebar-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: var(--text-color);
  text-decoration: none;
  transition: all 0.2s;
  border-left: 3px solid transparent;
}

.sidebar-link:hover {
  background-color: rgba(0, 0, 0, 0.03);
  color: var(--primary-color);
}

.sidebar-link.active {
  background-color: rgba(52, 152, 219, 0.1);
  color: var(--primary-color);
  border-left-color: var(--primary-color);
}

.sidebar-icon {
  width: 20px;
  margin-right: 0.75rem;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-text {
  font-size: 0.9rem;
  font-weight: 500;
}

.sidebar-footer {
  padding: 1rem 0;
  border-top: 1px solid var(--border-color);
}

/* Footer Styles */
.footer {
  background-color: var(--dark-color);
  color: white;
  text-align: center;
  padding: 1rem;
}

/* Card Styles */
.card {
  background-color: white;
  border-radius: 8px;
  box-shadow: var(--shadow);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

/* Button Styles */
.btn {
  display: inline-block;
  background-color: var(--primary-color);
  color: white;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
}

.btn-secondary {
  background-color: var(--secondary-color);
}

.btn-danger {
  background-color: var(--danger-color);
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

/* Form Styles */
.form-group {
  margin-bottom: 1rem;
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.5rem;
  font-size: 1rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
}

.mt-3 {
  margin-top: 1.5rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.alert {
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

.alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.form-control {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  font-size: 1rem;
}

/* Table Styles */
.table {
  width: 100%;
  border-collapse: collapse;
}

.table th, .table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.table th {
  background-color: var(--light-color);
  font-weight: 600;
}

.payment-row {
  background-color: rgba(46, 204, 113, 0.1);
}

.text-success {
  color: var(--secondary-color);
  font-weight: bold;
}

.badge {
  display: inline-block;
  padding: 0.25em 0.4em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
  color: white;
}

.bg-success {
  background-color: var(--secondary-color);
}

.bg-warning {
  background-color: var(--accent-color);
}

.bg-danger {
  background-color: var(--danger-color);
}

/* Alert Styles */
.alert {
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Dashboard Styles */
.dashboard-container {
  padding: 1.5rem;
  background-color: var(--dashboard-bg);
  border-radius: var(--card-border-radius);
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.dashboard-date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #666;
  font-size: 0.9rem;
}

.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Dashboard Stats Grid */
.dashboard-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.dashboard-stat-card {
  background-color: var(--card-bg);
  border-radius: var(--card-border-radius);
  box-shadow: var(--card-shadow);
  padding: 1.25rem;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.dashboard-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
}

.dashboard-stat-card.products::before {
  background-color: var(--products-color);
}

.dashboard-stat-card.vendors::before {
  background-color: var(--vendors-color);
}

.dashboard-stat-card.customers::before {
  background-color: var(--customers-color);
}

.dashboard-stat-card.sales::before {
  background-color: var(--sales-color);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  font-size: 1.25rem;
}

.products .stat-icon {
  color: var(--products-color);
  background-color: rgba(52, 152, 219, 0.1);
}

.vendors .stat-icon {
  color: var(--vendors-color);
  background-color: rgba(46, 204, 113, 0.1);
}

.customers .stat-icon {
  color: var(--customers-color);
  background-color: rgba(155, 89, 182, 0.1);
}

.sales .stat-icon {
  color: var(--sales-color);
  background-color: rgba(243, 156, 18, 0.1);
}

.stat-content {
  flex: 1;
}

.stat-content h3 {
  font-size: 1.5rem;
  margin: 0;
  font-weight: 600;
}

.stat-content p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.stat-link {
  position: absolute;
  right: 1.25rem;
  bottom: 1.25rem;
  font-size: 0.8rem;
  color: #666;
  text-decoration: none;
}

.stat-link:hover {
  text-decoration: underline;
}

/* Dashboard Main Grid */
.dashboard-main-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

@media (max-width: 992px) {
  .dashboard-main-grid {
    grid-template-columns: 1fr;
  }
}

/* Dashboard Card */
.dashboard-card {
  background-color: var(--card-bg);
  border-radius: var(--card-border-radius);
  box-shadow: var(--card-shadow);
  padding: 1.5rem;
}

.dashboard-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid var(--border-color);
}

.dashboard-card .card-header h2 {
  font-size: 1.25rem;
  margin: 0;
  font-weight: 600;
}

.card-actions {
  color: #666;
  cursor: pointer;
}

/* Financial Metrics */
.financial-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.metric-card {
  padding: 1.25rem;
  border-radius: var(--card-border-radius);
  display: flex;
  align-items: center;
}

.metric-card.profit {
  background-color: rgba(39, 174, 96, 0.1);
}

.metric-card.receivables {
  background-color: rgba(231, 76, 60, 0.1);
}

.metric-card.payables {
  background-color: rgba(142, 68, 173, 0.1);
}

.metric-card.cashflow {
  background-color: rgba(22, 160, 133, 0.1);
}

.metric-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  font-size: 1.25rem;
}

.profit .metric-icon {
  color: var(--profit-color);
}

.receivables .metric-icon {
  color: var(--receivables-color);
}

.payables .metric-icon {
  color: var(--payables-color);
}

.cashflow .metric-icon {
  color: var(--cashflow-color);
}

.metric-content h3 {
  font-size: 1.25rem;
  margin: 0;
  font-weight: 600;
}

.metric-content p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

/* Transactions */
.transactions-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.transaction-list {
  display: flex;
  flex-direction: column;
}

.transaction-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-color);
}

.transaction-item:last-child {
  border-bottom: none;
}

.transaction-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  font-size: 1rem;
}

.transaction-icon.sale {
  color: var(--sale-color);
  background-color: rgba(52, 152, 219, 0.1);
}

.transaction-icon.purchase {
  color: var(--purchase-color);
  background-color: rgba(46, 204, 113, 0.1);
}

.transaction-details {
  flex: 1;
}

.transaction-title {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.transaction-subtitle {
  font-size: 0.8rem;
  color: #666;
}

.transaction-amount {
  font-weight: 600;
  font-size: 1.1rem;
}

.view-all-link {
  display: block;
  text-align: center;
  padding: 0.75rem;
  margin-top: 0.5rem;
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.9rem;
  font-weight: 500;
}

.view-all-link:hover {
  text-decoration: underline;
}

.no-data {
  text-align: center;
  color: #666;
  padding: 1.5rem 0;
}

/* Balance Summary */
.balance-summary {
  margin-bottom: 1.5rem;
}

.balance-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
}

.balance-metric {
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: var(--card-border-radius);
}

.balance-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.balance-value {
  font-size: 1.25rem;
  font-weight: 600;
}
