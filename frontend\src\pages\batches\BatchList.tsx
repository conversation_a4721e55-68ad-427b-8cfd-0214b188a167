import { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { FaPlus, FaTrash, FaEye, FaEdit } from 'react-icons/fa';
import './BatchStyles.css';

interface Batch {
  id: number;
  batch_number: string;
  product_id: number;
  product_name: string;
  quantity_produced: number;
  production_date: string;
  total_cost: number;
  cost_per_unit: number;
}

const BatchList = () => {
  const [batches, setBatches] = useState<Batch[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBatches = async () => {
      try {
        const response = await axios.get('http://localhost:5000/api/batches');
        setBatches(response.data);
        setLoading(false);
      } catch (err) {
        setError('Failed to fetch batches. Please try again later.');
        setLoading(false);
        console.error('Error fetching batches:', err);
      }
    };

    fetchBatches();
  }, []);

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this batch?')) {
      try {
        await axios.delete(`http://localhost:5000/api/batches/${id}`);
        setBatches(batches.filter(batch => batch.id !== id));
      } catch (err) {
        setError('Failed to delete batch. Please try again later.');
        console.error('Error deleting batch:', err);
      }
    }
  };

  if (loading) {
    return <div className="batch-loading">Loading batches...</div>;
  }

  if (error) {
    return <div className="batch-error">{error}</div>;
  }

  return (
    <div className="batch-form-container">
      <div className="batch-header">
        <h1>Production Batches</h1>
        <div className="batch-header-actions">
          <Link to="/batches/new" className="batch-btn batch-btn-primary">
            <FaPlus /> Add Batch
          </Link>
        </div>
      </div>

      {batches.length === 0 ? (
        <div className="batch-card" style={{ textAlign: 'center', padding: '3rem' }}>
          <p style={{ fontSize: '1.1rem', color: '#666' }}>No batches found. Start by adding a production batch.</p>
          <Link to="/batches/new" className="batch-btn batch-btn-primary" style={{ marginTop: '1rem' }}>
            <FaPlus /> Add Your First Batch
          </Link>
        </div>
      ) : (
        <div className="batch-card">
          <table className="batch-table">
            <thead>
              <tr>
                <th>Batch Number</th>
                <th>Product</th>
                <th>Quantity</th>
                <th>Production Date</th>
                <th>Total Cost</th>
                <th>Cost Per Unit</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {batches.map(batch => (
                <tr key={batch.id}>
                  <td><strong>{batch.batch_number}</strong></td>
                  <td>{batch.product_name}</td>
                  <td>{batch.quantity_produced}</td>
                  <td>{new Date(batch.production_date).toLocaleDateString()}</td>
                  <td><span style={{ color: '#3498db', fontWeight: 600 }}>PKR {parseFloat(batch.total_cost || 0).toFixed(2)}</span></td>
                  <td><span style={{ color: '#2ecc71', fontWeight: 600 }}>PKR {parseFloat(batch.cost_per_unit || 0).toFixed(2)}</span></td>
                  <td className="batch-table-actions">
                    <Link to={`/batches/${batch.id}`} className="batch-btn batch-btn-icon" title="View Details">
                      <FaEye />
                    </Link>
                    <Link to={`/batches/edit/${batch.id}`} className="batch-btn batch-btn-icon batch-btn-primary" title="Edit Batch">
                      <FaEdit />
                    </Link>
                    <button
                      onClick={() => handleDelete(batch.id)}
                      className="batch-btn batch-btn-icon batch-btn-danger"
                      title="Delete"
                    >
                      <FaTrash />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default BatchList;
