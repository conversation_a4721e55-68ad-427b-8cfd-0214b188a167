const db = require('../db/db');

// @desc    Get all customers
// @route   GET /api/customers
// @access  Public
const getCustomers = async (req, res) => {
  try {
    // Get all customers
    const customersResult = await db.query(`
      SELECT * FROM customers ORDER BY name
    `);

    // For each customer, calculate the correct summary
    const result = { rows: [] };

    for (const customer of customersResult.rows) {
      // Get all transactions for this customer
      const transactionsResult = await db.query(
        'SELECT * FROM customer_transactions WHERE customer_id = $1',
        [customer.id]
      );

      // Calculate summary
      let total_sales = 0;
      let total_paid = 0;

      transactionsResult.rows.forEach(transaction => {
        if (transaction.transaction_type === 'payment') {
          total_paid += parseFloat(transaction.amount);
        } else {
          total_sales += parseFloat(transaction.amount);
        }
      });

      const balance = total_sales - total_paid;

      // Add customer with calculated summary to result
      result.rows.push({
        ...customer,
        total_sales,
        total_paid,
        balance
      });
    }

    res.status(200).json(result.rows);
  } catch (error) {
    console.error('Error fetching customers:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get single customer
// @route   GET /api/customers/:id
// @access  Public
const getCustomerById = async (req, res) => {
  try {
    const { id } = req.params;

    // Get customer details
    const customerResult = await db.query(
      'SELECT * FROM customers WHERE id = $1',
      [id]
    );

    if (customerResult.rows.length === 0) {
      return res.status(404).json({ message: 'Customer not found' });
    }

    // Get customer transactions
    const transactionsResult = await db.query(
      'SELECT * FROM customer_transactions WHERE customer_id = $1 ORDER BY transaction_date DESC',
      [id]
    );

    // Get customer sales
    const salesResult = await db.query(`
      SELECT
        s.*,
        (s.total_amount - s.paid_amount) as balance,
        json_agg(
          json_build_object(
            'id', si.id,
            'product_id', si.product_id,
            'product_name', p.name,
            'quantity', si.quantity,
            'unit_price', si.unit_price,
            'total_price', si.total_price,
            'profit', si.profit
          )
        ) as items
      FROM
        sales s
      JOIN
        sale_items si ON s.id = si.sale_id
      JOIN
        products p ON si.product_id = p.id
      WHERE
        s.customer_id = $1
      GROUP BY
        s.id
      ORDER BY
        s.sale_date DESC
    `, [id]);

    // Calculate summary
    const summary = {
      total_sales: 0,
      total_paid: 0,
      balance: 0
    };

    transactionsResult.rows.forEach(transaction => {
      if (transaction.transaction_type === 'sale') {
        summary.total_sales += parseFloat(transaction.amount);
      } else if (transaction.transaction_type === 'payment') {
        summary.total_paid += parseFloat(transaction.amount);
      }
    });

    summary.balance = summary.total_sales - summary.total_paid;

    res.status(200).json({
      ...customerResult.rows[0],
      transactions: transactionsResult.rows,
      sales: salesResult.rows,
      summary
    });
  } catch (error) {
    console.error('Error fetching customer:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Create a customer
// @route   POST /api/customers
// @access  Public
const createCustomer = async (req, res) => {
  try {
    const { name, contact, address, notes } = req.body;

    if (!name) {
      return res.status(400).json({ message: 'Customer name is required' });
    }

    const result = await db.query(
      'INSERT INTO customers (name, contact, address, notes) VALUES ($1, $2, $3, $4) RETURNING *',
      [name, contact, address, notes]
    );

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating customer:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Update a customer
// @route   PUT /api/customers/:id
// @access  Public
const updateCustomer = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, contact, address, notes } = req.body;

    if (!name) {
      return res.status(400).json({ message: 'Customer name is required' });
    }

    const result = await db.query(
      'UPDATE customers SET name = $1, contact = $2, address = $3, notes = $4, updated_at = CURRENT_TIMESTAMP WHERE id = $5 RETURNING *',
      [name, contact, address, notes, id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Customer not found' });
    }

    res.status(200).json(result.rows[0]);
  } catch (error) {
    console.error('Error updating customer:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Delete a customer
// @route   DELETE /api/customers/:id
// @access  Public
const deleteCustomer = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query(
      'DELETE FROM customers WHERE id = $1 RETURNING *',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Customer not found' });
    }

    res.status(200).json({ message: 'Customer deleted successfully' });
  } catch (error) {
    console.error('Error deleting customer:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Add a payment for a customer
// @route   POST /api/customers/:id/payments
// @access  Public
const addCustomerPayment = async (req, res) => {
  const client = await db.getClient();

  try {
    await client.query('BEGIN');

    const { id } = req.params;
    const { transaction_date, amount, notes, sale_id } = req.body;

    // Validate required fields
    if (!amount || parseFloat(amount) <= 0) {
      return res.status(400).json({ message: 'Valid payment amount is required' });
    }

    // Check if customer exists
    const customerCheck = await client.query(
      'SELECT * FROM customers WHERE id = $1',
      [id]
    );

    if (customerCheck.rows.length === 0) {
      return res.status(404).json({ message: 'Customer not found' });
    }

    // If sale_id is provided, update that specific sale
    if (sale_id) {
      const saleResult = await client.query(
        'SELECT * FROM sales WHERE id = $1 AND customer_id = $2',
        [sale_id, id]
      );

      if (saleResult.rows.length === 0) {
        return res.status(404).json({ message: 'Sale not found' });
      }

      const sale = saleResult.rows[0];
      const newPaidAmount = parseFloat(sale.paid_amount) + parseFloat(amount);
      let newPaymentStatus = 'partial';

      if (newPaidAmount >= parseFloat(sale.total_amount)) {
        newPaymentStatus = 'paid';
      }

      await client.query(
        'UPDATE sales SET paid_amount = $1, payment_status = $2, updated_at = CURRENT_TIMESTAMP WHERE id = $3',
        [newPaidAmount, newPaymentStatus, sale_id]
      );
    }

    // Create a new transaction record for the payment
    await client.query(
      `INSERT INTO customer_transactions
        (customer_id, transaction_date, amount, transaction_type, notes)
       VALUES
        ($1, $2, $3, 'payment', $4)
       RETURNING *`,
      [
        id,
        transaction_date || new Date(),
        amount,
        notes || 'Customer payment'
      ]
    );

    await client.query('COMMIT');

    // Get updated customer data
    const customerResult = await client.query(
      'SELECT * FROM customers WHERE id = $1',
      [id]
    );

    const transactionsResult = await client.query(
      'SELECT * FROM customer_transactions WHERE customer_id = $1 ORDER BY transaction_date DESC',
      [id]
    );

    // Calculate summary
    const summary = {
      total_sales: 0,
      total_paid: 0,
      balance: 0
    };

    transactionsResult.rows.forEach(transaction => {
      if (transaction.transaction_type === 'payment') {
        summary.total_paid += parseFloat(transaction.amount);
      } else {
        summary.total_sales += parseFloat(transaction.amount);
      }
    });

    summary.balance = summary.total_sales - summary.total_paid;

    res.status(200).json({
      ...customerResult.rows[0],
      transactions: transactionsResult.rows,
      summary,
      message: 'Payment added successfully'
    });
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error adding customer payment:', error);
    res.status(500).json({ message: 'Server error' });
  } finally {
    client.release();
  }
};

// @desc    Get all transactions for a customer
// @route   GET /api/customers/:id/transactions
// @access  Public
const getCustomerTransactions = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query(
      'SELECT * FROM customer_transactions WHERE customer_id = $1 ORDER BY transaction_date DESC',
      [id]
    );

    res.status(200).json(result.rows);
  } catch (error) {
    console.error('Error fetching customer transactions:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

module.exports = {
  getCustomers,
  getCustomerById,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  addCustomerPayment,
  getCustomerTransactions
};
