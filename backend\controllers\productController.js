const db = require('../db/db');

// @desc    Get all products
// @route   GET /api/products
// @access  Public
const getProducts = async (req, res) => {
  try {
    const result = await db.query(
      'SELECT p.*, COALESCE(i.quantity, 0) as stock FROM products p LEFT JOIN inventory i ON p.id = i.product_id ORDER BY p.name'
    );
    res.status(200).json(result.rows);
  } catch (error) {
    console.error('Error fetching products:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Get single product
// @route   GET /api/products/:id
// @access  Public
const getProductById = async (req, res) => {
  try {
    const { id } = req.params;

    // Get product details
    const productResult = await db.query(
      'SELECT p.*, COALESCE(i.quantity, 0) as stock FROM products p LEFT JOIN inventory i ON p.id = i.product_id WHERE p.id = $1',
      [id]
    );

    if (productResult.rows.length === 0) {
      return res.status(404).json({ message: 'Product not found' });
    }

    // Get batch history with vendor information
    const batchesResult = await db.query(
      `SELECT b.id, b.batch_number, b.quantity_produced, b.production_date, b.total_cost, b.cost_per_unit
       FROM batches b
       WHERE b.product_id = $1
       ORDER BY b.production_date DESC`,
      [id]
    );

    // Get materials and vendors for each batch
    for (const batch of batchesResult.rows) {
      const materialsResult = await db.query(
        `SELECT bm.*, v.name as vendor_name
         FROM batch_materials bm
         LEFT JOIN vendors v ON bm.vendor_id = v.id
         WHERE bm.batch_id = $1
         ORDER BY bm.id`,
        [batch.id]
      );

      batch.materials = materialsResult.rows;
    }

    // Get sales history
    const salesResult = await db.query(
      'SELECT si.id, si.quantity, si.unit_price, si.total_price, si.profit, s.sale_date FROM sale_items si JOIN sales s ON si.sale_id = s.id WHERE si.product_id = $1 ORDER BY s.sale_date DESC',
      [id]
    );

    res.status(200).json({
      ...productResult.rows[0],
      batches: batchesResult.rows,
      sales: salesResult.rows
    });
  } catch (error) {
    console.error('Error fetching product:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Create a product
// @route   POST /api/products
// @access  Public
const createProduct = async (req, res) => {
  try {
    const { name, description } = req.body;

    if (!name) {
      return res.status(400).json({ message: 'Product name is required' });
    }

    // Start a transaction
    const client = await db.pool.connect();

    try {
      await client.query('BEGIN');

      // Insert product
      const productResult = await client.query(
        'INSERT INTO products (name, description) VALUES ($1, $2) RETURNING *',
        [name, description]
      );

      // Initialize inventory
      await client.query(
        'INSERT INTO inventory (product_id, quantity) VALUES ($1, 0)',
        [productResult.rows[0].id]
      );

      await client.query('COMMIT');

      res.status(201).json(productResult.rows[0]);
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error creating product:', error);
    console.error('Error details:', error.stack);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// @desc    Update a product
// @route   PUT /api/products/:id
// @access  Public
const updateProduct = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description } = req.body;

    if (!name) {
      return res.status(400).json({ message: 'Product name is required' });
    }

    const result = await db.query(
      'UPDATE products SET name = $1, description = $2, updated_at = CURRENT_TIMESTAMP WHERE id = $3 RETURNING *',
      [name, description, id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Product not found' });
    }

    res.status(200).json(result.rows[0]);
  } catch (error) {
    console.error('Error updating product:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

// @desc    Delete a product
// @route   DELETE /api/products/:id
// @access  Public
const deleteProduct = async (req, res) => {
  try {
    const { id } = req.params;

    const result = await db.query(
      'DELETE FROM products WHERE id = $1 RETURNING *',
      [id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ message: 'Product not found' });
    }

    res.status(200).json({ message: 'Product deleted successfully' });
  } catch (error) {
    console.error('Error deleting product:', error);
    res.status(500).json({ message: 'Server error' });
  }
};

module.exports = {
  getProducts,
  getProductById,
  createProduct,
  updateProduct,
  deleteProduct
};
