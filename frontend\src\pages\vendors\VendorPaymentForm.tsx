import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { FaArrowLeft, FaSave, FaMoneyBillWave } from 'react-icons/fa';
import '../../styles/SharedStyles.css';

interface PaymentFormData {
  transaction_date: string;
  amount: number;
  notes: string;
  material_id?: number;
}

interface Material {
  id: number;
  material_name: string;
  total_amount: number;
  paid_amount: number;
  payment_status: string;
}

const VendorPaymentForm = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [formData, setFormData] = useState<PaymentFormData>({
    transaction_date: new Date().toISOString().split('T')[0],
    amount: 0,
    notes: '',
    material_id: undefined
  });
  const [materials, setMaterials] = useState<Material[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    const fetchVendorTransactions = async () => {
      try {
        const response = await axios.get(`http://localhost:5000/api/vendors/${id}`);
        const unpaidMaterials = response.data.transactions.filter(
          (t: any) => t.payment_status !== 'paid' && t.material_name !== 'Payment'
        );
        setMaterials(unpaidMaterials);
        setLoading(false);
      } catch (err) {
        setError('Failed to fetch vendor transactions. Please try again later.');
        setLoading(false);
        console.error('Error fetching vendor transactions:', err);
      }
    };

    fetchVendorTransactions();
  }, [id]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    if (name === 'material_id') {
      if (value === '') {
        setFormData(prev => ({ ...prev, material_id: undefined }));
      } else {
        const materialId = parseInt(value);
        const selectedMaterial = materials.find(m => m.id === materialId);

        if (selectedMaterial) {
          const remainingAmount = selectedMaterial.total_amount - selectedMaterial.paid_amount;
          setFormData(prev => ({
            ...prev,
            material_id: materialId,
            amount: remainingAmount
          }));
        }
      }
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      await axios.post(`http://localhost:5000/api/vendors/${id}/payments`, formData);
      setSuccess('Payment added successfully!');
      setTimeout(() => navigate(`/vendors/${id}`), 1500);
    } catch (err) {
      setError('Failed to add payment. Please try again later.');
      console.error('Error adding payment:', err);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return <div className="app-loading">Loading vendor details...</div>;
  }

  return (
    <div className="app-form-container">
      <div className="app-header">
        <h1>
          <FaMoneyBillWave style={{ marginRight: '0.5rem', color: '#2ecc71' }} /> Add Vendor Payment
        </h1>
        <div className="app-header-actions">
          <Link to={`/vendors/${id}`} className="app-btn app-btn-sm">
            <FaArrowLeft /> Back to Vendor
          </Link>
        </div>
      </div>

      <div className="app-card">
        {error && <div className="app-error">{error}</div>}
        {success && <div className="app-success">{success}</div>}

        <form onSubmit={handleSubmit}>
          <div className="app-form-row">
            <div className="app-form-col">
              <div className="app-form-group">
                <label htmlFor="transaction_date" className="app-form-label">Payment Date</label>
                <input
                  type="date"
                  id="transaction_date"
                  name="transaction_date"
                  className="app-form-control"
                  value={formData.transaction_date}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>

            <div className="app-form-col">
              <div className="app-form-group">
                <label htmlFor="amount" className="app-form-label">Payment Amount (PKR)</label>
                <input
                  type="number"
                  id="amount"
                  name="amount"
                  className="app-form-control"
                  value={formData.amount}
                  onChange={handleChange}
                  min="0.01"
                  step="0.01"
                  required
                />
              </div>
            </div>
          </div>

          <div className="app-form-group">
            <label htmlFor="material_id" className="app-form-label">Apply to Specific Purchase (Optional)</label>
            <select
              id="material_id"
              name="material_id"
              className="app-form-control"
              value={formData.material_id || ''}
              onChange={handleChange}
            >
              <option value="">General Payment (not for specific purchase)</option>
              {materials.map(material => (
                <option key={material.id} value={material.id}>
                  {material.material_name} - PKR {(material.total_amount - material.paid_amount).toFixed(2)} remaining
                </option>
              ))}
            </select>
          </div>

          <div className="app-form-group">
            <label htmlFor="notes" className="app-form-label">Notes</label>
            <textarea
              id="notes"
              name="notes"
              className="app-form-control"
              value={formData.notes}
              onChange={handleChange}
              placeholder="Enter any additional notes"
              rows={3}
            />
          </div>

          <div style={{ textAlign: 'center', marginTop: '2rem' }}>
            <button
              type="submit"
              className="app-btn app-btn-primary"
              disabled={submitting}
              style={{ minWidth: '200px' }}
            >
              <FaSave /> {submitting ? 'Processing...' : 'Make Payment'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default VendorPaymentForm;
