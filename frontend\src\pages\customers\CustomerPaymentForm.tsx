import { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { FaArrowLeft, FaSave, FaMoneyBillWave } from 'react-icons/fa';
import '../../styles/SharedStyles.css';

interface PaymentFormData {
  transaction_date: string;
  amount: number;
  notes: string;
  sale_id?: number;
}

interface Sale {
  id: number;
  invoice_number: string;
  sale_date: string;
  total_amount: number;
  paid_amount: number;
  balance: number;
  payment_status: string;
}

const CustomerPaymentForm = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [formData, setFormData] = useState<PaymentFormData>({
    transaction_date: new Date().toISOString().split('T')[0],
    amount: 0,
    notes: '',
    sale_id: undefined
  });

  const [sales, setSales] = useState<Sale[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    const fetchCustomerSales = async () => {
      try {
        const response = await axios.get(`http://localhost:5000/api/customers/${id}`);
        // Filter sales with unpaid or partial status
        const unpaidSales = response.data.sales?.filter(
          (s: Sale) => s.payment_status !== 'paid'
        ) || [];

        // Make sure each sale has a balance property
        const salesWithBalance = unpaidSales.map((sale: any) => ({
          ...sale,
          balance: parseFloat(sale.balance || (sale.total_amount - sale.paid_amount))
        }));

        setSales(salesWithBalance);
        setLoading(false);
      } catch (err) {
        setError('Failed to fetch customer sales. Please try again later.');
        setLoading(false);
        console.error('Error fetching customer sales:', err);
      }
    };

    fetchCustomerSales();
  }, [id]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    if (name === 'sale_id') {
      if (value === '') {
        setFormData(prev => ({ ...prev, sale_id: undefined }));
      } else {
        const saleId = parseInt(value);
        const selectedSale = sales.find(s => s.id === saleId);

        if (selectedSale) {
          const remainingAmount = selectedSale.balance;
          setFormData(prev => ({
            ...prev,
            sale_id: saleId,
            amount: remainingAmount
          }));
        }
      }
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);
    setSuccess(null);

    try {
      await axios.post(`http://localhost:5000/api/customers/${id}/payments`, formData);
      setSuccess('Payment added successfully!');
      setTimeout(() => navigate(`/customers/${id}`), 1500);
    } catch (err) {
      setError('Failed to add payment. Please try again later.');
      console.error('Error adding payment:', err);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return <div className="app-loading">Loading customer details...</div>;
  }

  return (
    <div className="app-form-container">
      <div className="app-header">
        <h1>
          <FaMoneyBillWave style={{ marginRight: '0.5rem', color: '#2ecc71' }} /> Add Customer Payment
        </h1>
        <div className="app-header-actions">
          <Link to={`/customers/${id}`} className="app-btn app-btn-sm">
            <FaArrowLeft /> Back to Customer
          </Link>
        </div>
      </div>

      <div className="app-card">
        {error && <div className="app-error">{error}</div>}
        {success && <div className="app-success">{success}</div>}

        <form onSubmit={handleSubmit}>
          <div className="app-form-row">
            <div className="app-form-col">
              <div className="app-form-group">
                <label htmlFor="transaction_date" className="app-form-label">Payment Date</label>
                <input
                  type="date"
                  id="transaction_date"
                  name="transaction_date"
                  className="app-form-control"
                  value={formData.transaction_date}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>

            <div className="app-form-col">
              <div className="app-form-group">
                <label htmlFor="amount" className="app-form-label">Payment Amount (PKR)</label>
                <input
                  type="number"
                  id="amount"
                  name="amount"
                  className="app-form-control"
                  value={formData.amount}
                  onChange={handleChange}
                  min="0.01"
                  step="0.01"
                  required
                />
              </div>
            </div>
          </div>

          <div className="app-form-group">
            <label htmlFor="sale_id" className="app-form-label">Apply to Specific Sale (Optional)</label>
            <select
              id="sale_id"
              name="sale_id"
              className="app-form-control"
              value={formData.sale_id || ''}
              onChange={handleChange}
            >
              <option value="">General Payment (not for specific sale)</option>
              {sales.map(sale => (
                <option key={sale.id} value={sale.id}>
                  {sale.invoice_number} - PKR {sale.balance.toFixed(2)} remaining
                </option>
              ))}
            </select>
          </div>

          <div className="app-form-group">
            <label htmlFor="notes" className="app-form-label">Notes</label>
            <textarea
              id="notes"
              name="notes"
              className="app-form-control"
              value={formData.notes}
              onChange={handleChange}
              placeholder="Enter any additional notes"
              rows={3}
            />
          </div>

          <div style={{ textAlign: 'center', marginTop: '2rem' }}>
            <button
              type="submit"
              className="app-btn app-btn-primary"
              disabled={submitting}
              style={{ minWidth: '200px' }}
            >
              <FaSave /> {submitting ? 'Processing...' : 'Make Payment'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CustomerPaymentForm;
